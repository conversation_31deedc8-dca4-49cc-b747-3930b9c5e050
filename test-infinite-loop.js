const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch({ headless: 'new' });
  const page = await browser.newPage();
  
  // 监听控制台错误
  page.on('console', msg => {
    if (msg.type() === 'error') {
      console.error('页面错误:', msg.text());
    }
  });
  
  // 监听页面错误
  page.on('pageerror', error => {
    console.error('页面崩溃错误:', error.message);
  });
  
  try {
    console.log('正在访问页面...');
    await page.goto('http://localhost:3000/data/list/us_pmn?d=us_pmn', {
      waitUntil: 'networkidle0',
      timeout: 30000
    });
    
    console.log('页面加载成功！');
    
    // 等待一下看是否有延迟的错误
    await page.waitForTimeout(3000);
    
    // 检查是否有错误消息
    const errorElement = await page.$eval('body', el => {
      const text = el.textContent || '';
      if (text.includes('Maximum update depth exceeded')) {
        return '发现无限循环错误！';
      }
      if (text.includes('Something went wrong')) {
        return '发现其他错误！';
      }
      return null;
    }).catch(() => null);
    
    if (errorElement) {
      console.error(errorElement);
      process.exit(1);
    } else {
      console.log('✅ 页面正常运行，没有发现无限循环错误！');
    }
    
  } catch (error) {
    console.error('测试失败:', error.message);
    process.exit(1);
  } finally {
    await browser.close();
  }
})();

