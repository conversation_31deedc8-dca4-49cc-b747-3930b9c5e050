# 搜索功能修复测试

## 问题描述
之前的问题：
1. 在搜索输入框中输入内容时，会立即触发搜索，而不是等用户点击 Apply 按钮
2. URL 地址栏会实时变化
3. 搜索失败并报错："内部服务器错误"

## 修复内容
1. 禁用了搜索管理器的自动 URL 同步 (`autoSyncUrl: false`)
2. 禁用了从 URL 自动加载搜索状态 (`loadFromUrl: false`)
3. 修改了 `handleFilterChange` 函数，不再自动同步到搜索管理系统
4. 修改了 URL 参数监听逻辑，只在初始加载时执行一次
5. 在 `handleSearch` 和 `resetFilters` 函数中手动管理 URL 同步

## 测试步骤
1. 访问 http://localhost:3000/data/list/us_pmn
2. 在 device name 搜索框中输入 "dental"
3. 验证：
   - 不应该立即触发搜索
   - URL 地址栏不应该变化
   - 不应该出现错误
4. 点击 "Apply Filters" 按钮
5. 验证：
   - 应该触发搜索
   - URL 地址栏应该更新
   - 搜索应该成功执行

## 预期结果
- 输入时不触发搜索，只有点击 Apply 按钮才搜索
- URL 只在点击 Apply 按钮时更新
- 搜索功能正常工作，不再报错
