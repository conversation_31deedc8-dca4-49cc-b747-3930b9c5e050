/**
 * Zustand 搜索状态管理 Store
 * 提供全局搜索条件管理、URL同步、搜索历史和模板功能
 */

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { 
  SearchState, 
  SearchCondition, 
  SearchFilters, 
  SearchHistoryItem, 
  SearchTemplate,
  PaginationState,
  SortState,
  DEFAULT_SEARCH_STATE,
  DEFAULT_SEARCH_CONFIG
} from '@/types/search';

// Store 接口定义
interface SearchStore extends SearchState {
  // 基础操作
  setDatabase: (database: string) => void;
  setKeyword: (keyword: string) => void;
  setFilters: (filters: SearchFilters) => void;
  updateFilter: (key: string, value: unknown) => void;
  clearFilters: () => void;
  
  // 搜索条件操作
  setConditions: (conditions: SearchCondition[]) => void;
  addCondition: (condition: SearchCondition) => void;
  updateCondition: (conditionId: string, updates: Partial<SearchCondition>) => void;
  removeCondition: (conditionId: string) => void;
  clearConditions: () => void;
  
  // 分页和排序
  setPagination: (pagination: Partial<PaginationState>) => void;
  setSort: (sort: Partial<SortState>) => void;
  
  // 搜索状态
  setSearching: (isSearching: boolean) => void;
  setLastSearchResult: (resultCount: number, duration: number) => void;
  
  // 历史记录管理
  addToHistory: (searchParams: {
    keyword: string;
    conditions: SearchCondition[];
    filters: SearchFilters;
    database: string;
    resultCount?: number;
    searchDuration?: number;
  }) => void;
  loadFromHistory: (historyId: string) => void;
  clearHistory: () => void;
  removeHistoryItem: (historyId: string) => void;
  
  // 模板管理
  saveAsTemplate: (
    name: string, 
    description?: string, 
    isPublic?: boolean,
    tags?: string[]
  ) => void;
  loadTemplate: (templateId: string) => void;
  updateTemplate: (templateId: string, updates: Partial<SearchTemplate>) => void;
  deleteTemplate: (templateId: string) => void;
  
  // UI 状态
  toggleHistory: () => void;
  toggleTemplates: () => void;
  toggleConditionExpanded: (conditionId: string) => void;
  
  // URL 同步
  syncFromUrl: (params: URLSearchParams) => void;
  getUrlParams: () => URLSearchParams;
  
  // 重置功能
  resetSearch: () => void;
  resetToDatabase: (database: string) => void;
  
  // 实用功能
  getCurrentSearchSummary: () => string;
  hasActiveSearch: () => boolean;
  getActiveFiltersCount: () => number;
}

// 生成唯一ID的辅助函数
const generateId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
};

// 生成搜索描述的辅助函数
const generateSearchDescription = (
  keyword: string,
  conditions: SearchCondition[],
  filters: SearchFilters
): string => {
  const parts: string[] = [];
  
  if (keyword) {
    parts.push(`关键词: "${keyword}"`);
  }
  
  if (conditions.length > 0) {
    parts.push(`高级条件: ${conditions.length}个`);
  }
  
  const filterCount = Object.keys(filters).filter(key => 
    filters[key] !== undefined && 
    filters[key] !== null && 
    filters[key] !== ''
  ).length;
  
  if (filterCount > 0) {
    parts.push(`筛选器: ${filterCount}个`);
  }
  
  return parts.length > 0 ? parts.join(', ') : '空搜索';
};

// 清理过期历史记录的辅助函数
const cleanupHistory = (history: SearchHistoryItem[]): SearchHistoryItem[] => {
  const now = Date.now();
  const ttl = DEFAULT_SEARCH_CONFIG.historyTTL;
  
  return history
    .filter(item => now - item.timestamp < ttl)
    .slice(0, DEFAULT_SEARCH_CONFIG.maxHistoryItems);
};

// 创建 Zustand store
export const useSearchStore = create<SearchStore>()(
  subscribeWithSelector((set, get) => ({
    // 初始状态
    ...DEFAULT_SEARCH_STATE,
    
    // 基础操作
    setDatabase: (database: string) => {
      set(state => ({
        ...state,
        currentDatabase: database,
        // 切换数据库时重置搜索状态，但保留模板
        keyword: '',
        conditions: [],
        filters: {},
        pagination: { ...DEFAULT_SEARCH_STATE.pagination },
        sort: { ...DEFAULT_SEARCH_STATE.sort },
      }));
    },
    
    setKeyword: (keyword: string) => {
      set(state => ({
        ...state,
        keyword,
        pagination: { ...state.pagination, page: 1 }, // 重置页码
      }));
    },
    
    setFilters: (filters: SearchFilters) => {
      set(state => ({
        ...state,
        filters,
        pagination: { ...state.pagination, page: 1 }, // 重置页码
      }));
    },
    
    updateFilter: (key: string, value: unknown) => {
      set(state => ({
        ...state,
        filters: {
          ...state.filters,
          [key]: value,
        },
        pagination: { ...state.pagination, page: 1 }, // 重置页码
      }));
    },
    
    clearFilters: () => {
      set(state => ({
        ...state,
        filters: {},
        pagination: { ...state.pagination, page: 1 },
      }));
    },
    
    // 搜索条件操作
    setConditions: (conditions: SearchCondition[]) => {
      set(state => ({
        ...state,
        conditions,
        pagination: { ...state.pagination, page: 1 },
      }));
    },
    
    addCondition: (condition: SearchCondition) => {
      set(state => ({
        ...state,
        conditions: [...state.conditions, condition],
        pagination: { ...state.pagination, page: 1 },
      }));
    },
    
    updateCondition: (conditionId: string, updates: Partial<SearchCondition>) => {
      set(state => ({
        ...state,
        conditions: state.conditions.map(condition =>
          condition.id === conditionId 
            ? { ...condition, ...updates }
            : condition
        ),
        pagination: { ...state.pagination, page: 1 },
      }));
    },
    
    removeCondition: (conditionId: string) => {
      set(state => {
        const newConditions = state.conditions.filter(c => c.id !== conditionId);
        // 移除第一个条件的逻辑操作符
        if (newConditions.length > 0 && newConditions[0].logic) {
          newConditions[0] = { ...newConditions[0], logic: undefined };
        }
        
        return {
          ...state,
          conditions: newConditions,
          expandedConditions: new Set(
            Array.from(state.expandedConditions).filter(id => id !== conditionId)
          ),
          pagination: { ...state.pagination, page: 1 },
        };
      });
    },
    
    clearConditions: () => {
      set(state => ({
        ...state,
        conditions: [],
        expandedConditions: new Set(),
        pagination: { ...state.pagination, page: 1 },
      }));
    },
    
    // 分页和排序
    setPagination: (pagination: Partial<PaginationState>) => {
      set(state => ({
        ...state,
        pagination: { ...state.pagination, ...pagination },
      }));
    },
    
    setSort: (sort: Partial<SortState>) => {
      set(state => ({
        ...state,
        sort: { ...state.sort, ...sort },
        pagination: { ...state.pagination, page: 1 }, // 排序变更时重置页码
      }));
    },
    
    // 搜索状态
    setSearching: (isSearching: boolean) => {
      set(state => ({
        ...state,
        isSearching,
        lastSearchTime: isSearching ? Date.now() : state.lastSearchTime,
      }));
    },
    
    setLastSearchResult: (resultCount: number, _duration: number) => {
      set(state => ({
        ...state,
        resultCount,
        isSearching: false,
      }));
    },
    
    // 历史记录管理
    addToHistory: (searchParams) => {
      const { keyword, conditions, filters, database, resultCount, searchDuration } = searchParams;
      
      set(state => {
        const historyItem: SearchHistoryItem = {
          id: generateId(),
          timestamp: Date.now(),
          conditions: [...conditions],
          filters: { ...filters },
          keyword,
          database,
          resultCount,
          searchDuration,
          description: generateSearchDescription(keyword, conditions, filters),
        };
        
        // 避免重复的历史记录
        const existingIndex = state.history.findIndex(item =>
          item.keyword === keyword &&
          item.database === database &&
          JSON.stringify(item.conditions) === JSON.stringify(conditions) &&
          JSON.stringify(item.filters) === JSON.stringify(filters)
        );
        
        let newHistory = [...state.history];
        
        if (existingIndex >= 0) {
          // 更新现有记录的时间戳和结果
          newHistory[existingIndex] = {
            ...newHistory[existingIndex],
            timestamp: historyItem.timestamp,
            resultCount,
            searchDuration,
          };
          // 移动到最前面
          const updatedItem = newHistory.splice(existingIndex, 1)[0];
          newHistory.unshift(updatedItem);
        } else {
          // 添加新记录
          newHistory.unshift(historyItem);
        }
        
        // 清理过期和超量的历史记录
        newHistory = cleanupHistory(newHistory);
        
        return {
          ...state,
          history: newHistory,
        };
      });
    },
    
    loadFromHistory: (historyId: string) => {
      const state = get();
      const historyItem = state.history.find(item => item.id === historyId);
      
      if (historyItem) {
        set({
          ...state,
          keyword: historyItem.keyword,
          conditions: [...historyItem.conditions],
          filters: { ...historyItem.filters },
          currentDatabase: historyItem.database,
          pagination: { ...state.pagination, page: 1 },
        });
      }
    },
    
    clearHistory: () => {
      set(state => ({
        ...state,
        history: [],
      }));
    },
    
    removeHistoryItem: (historyId: string) => {
      set(state => ({
        ...state,
        history: state.history.filter(item => item.id !== historyId),
      }));
    },
    
    // 模板管理
    saveAsTemplate: (name: string, description = '', isPublic = false, tags = []) => {
      const state = get();
      
      const template: SearchTemplate = {
        id: generateId(),
        name,
        description,
        conditions: [...state.conditions],
        filters: { ...state.filters },
        isPublic,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        usageCount: 0,
        tags,
      };
      
      set(currentState => {
        const newTemplates = [...currentState.templates, template]
          .slice(0, DEFAULT_SEARCH_CONFIG.maxTemplates);
        
        return {
          ...currentState,
          templates: newTemplates,
        };
      });
    },
    
    loadTemplate: (templateId: string) => {
      const state = get();
      const template = state.templates.find(t => t.id === templateId);
      
      if (template) {
        set({
          ...state,
          conditions: [...template.conditions],
          filters: { ...template.filters },
          pagination: { ...state.pagination, page: 1 },
        });
        
        // 更新模板使用次数
        set(currentState => ({
          ...currentState,
          templates: currentState.templates.map(t =>
            t.id === templateId 
              ? { ...t, usageCount: t.usageCount + 1, updatedAt: Date.now() }
              : t
          ),
        }));
      }
    },
    
    updateTemplate: (templateId: string, updates: Partial<SearchTemplate>) => {
      set(state => ({
        ...state,
        templates: state.templates.map(template =>
          template.id === templateId
            ? { ...template, ...updates, updatedAt: Date.now() }
            : template
        ),
      }));
    },
    
    deleteTemplate: (templateId: string) => {
      set(state => ({
        ...state,
        templates: state.templates.filter(t => t.id !== templateId),
      }));
    },
    
    // UI 状态
    toggleHistory: () => {
      set(state => ({
        ...state,
        showHistory: !state.showHistory,
        showTemplates: false, // 互斥显示
      }));
    },
    
    toggleTemplates: () => {
      set(state => ({
        ...state,
        showTemplates: !state.showTemplates,
        showHistory: false, // 互斥显示
      }));
    },
    
    toggleConditionExpanded: (conditionId: string) => {
      set(state => {
        const newExpanded = new Set(state.expandedConditions);
        if (newExpanded.has(conditionId)) {
          newExpanded.delete(conditionId);
        } else {
          newExpanded.add(conditionId);
        }
        
        return {
          ...state,
          expandedConditions: newExpanded,
        };
      });
    },
    
    // URL 同步
    syncFromUrl: (params: URLSearchParams) => {
      const state = get();
      
      const updates: Partial<SearchState> = {};
      
      if (params.has('q')) {
        updates.keyword = params.get('q') || '';
      }
      
      if (params.has('page')) {
        const page = parseInt(params.get('page') || '1');
        updates.pagination = { ...state.pagination, page: page > 0 ? page : 1 };
      }
      
      if (params.has('limit')) {
        const limit = parseInt(params.get('limit') || '20');
        updates.pagination = { 
          ...updates.pagination || state.pagination, 
          limit: limit > 0 ? Math.min(limit, 100) : 20 
        };
      }
      
      if (params.has('sortBy')) {
        updates.sort = { 
          ...state.sort, 
          field: params.get('sortBy') || '_score' 
        };
      }
      
      if (params.has('sortOrder')) {
        const order = params.get('sortOrder');
        if (order === 'asc' || order === 'desc') {
          updates.sort = { 
            ...updates.sort || state.sort, 
            order 
          };
        }
      }
      
      if (params.has('filters')) {
        try {
          const filters = JSON.parse(params.get('filters') || '{}');
          updates.filters = filters;
        } catch (error) {
          console.warn('Failed to parse filters from URL:', error);
        }
      }
      
      if (params.has('conditions')) {
        try {
          const conditions = JSON.parse(params.get('conditions') || '[]');
          updates.conditions = conditions;
        } catch (error) {
          console.warn('Failed to parse conditions from URL:', error);
        }
      }
      
      if (Object.keys(updates).length > 0) {
        set(currentState => ({
          ...currentState,
          ...updates,
        }));
      }
    },
    
    getUrlParams: (): URLSearchParams => {
      const state = get();
      const params = new URLSearchParams();
      
      // 只同步核心参数到 URL
      if (state.keyword) {
        params.set('q', state.keyword);
      }
      
      if (state.pagination.page > 1) {
        params.set('page', state.pagination.page.toString());
      }
      
      if (state.pagination.limit !== 20) {
        params.set('limit', state.pagination.limit.toString());
      }
      
      if (state.sort.field !== '_score') {
        params.set('sortBy', state.sort.field);
      }
      
      if (state.sort.order !== 'desc') {
        params.set('sortOrder', state.sort.order);
      }
      
      // 只有在有复杂筛选条件时才序列化到 URL
      const hasFilters = Object.keys(state.filters).some(key =>
        state.filters[key] !== undefined &&
        state.filters[key] !== null &&
        state.filters[key] !== ''
      );
      
      if (hasFilters) {
        params.set('filters', JSON.stringify(state.filters));
      }
      
      if (state.conditions.length > 0) {
        params.set('conditions', JSON.stringify(state.conditions));
      }
      
      return params;
    },
    
    // 重置功能
    resetSearch: () => {
      set(state => ({
        ...DEFAULT_SEARCH_STATE,
        currentDatabase: state.currentDatabase,
        history: state.history,
        templates: state.templates,
      }));
    },
    
    resetToDatabase: (database: string) => {
      set(state => ({
        ...DEFAULT_SEARCH_STATE,
        currentDatabase: database,
        history: state.history,
        templates: state.templates,
      }));
    },
    
    // 实用功能
    getCurrentSearchSummary: (): string => {
      const state = get();
      return generateSearchDescription(state.keyword, state.conditions, state.filters);
    },
    
    hasActiveSearch: (): boolean => {
      const state = get();
      return !!(
        state.keyword ||
        state.conditions.length > 0 ||
        Object.keys(state.filters).some(key =>
          state.filters[key] !== undefined &&
          state.filters[key] !== null &&
          state.filters[key] !== ''
        )
      );
    },
    
    getActiveFiltersCount: (): number => {
      const state = get();
      return Object.keys(state.filters).filter(key =>
        state.filters[key] !== undefined &&
        state.filters[key] !== null &&
        state.filters[key] !== ''
      ).length;
    },
  }))
);

// 便于在 React DevTools 中调试
if (typeof window !== 'undefined') {
  (window as any).searchStore = useSearchStore;
}



