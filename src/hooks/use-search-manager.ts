/**
 * 搜索条件管理 Hook
 * 提供统一的搜索状态管理接口，整合 Zustand store 和 URL 同步
 */

import { useCallback, useEffect, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSearchStore } from '@/stores/searchStore';
import { 
  urlParamsToSearchState, 
  updateBrowserUrl, 

  extractSearchStateFromCurrentUrl 
} from '@/lib/search-url-utils';
import {
  useSearchPerformance,

  diagnoseSearchPerformance
} from '@/lib/search-performance';
import {
  classifyError,
  searchRetryManager,
  userFeedback,
  searchLogger,
  searchHealthChecker,
  SearchErrorRecovery
} from '@/lib/search-error-handling';
import { 
  SearchCondition, 
  SearchFilters, 

} from '@/types/search';

// Hook 返回类型
export interface UseSearchManagerReturn {
  // 当前搜索状态
  searchState: ReturnType<typeof useSearchStore.getState>;
  
  // 基础搜索操作
  setKeyword: (keyword: string) => void;
  setFilters: (filters: SearchFilters) => void;
  updateFilter: (key: string, value: unknown) => void;
  clearFilters: () => void;
  
  // 搜索条件操作
  addCondition: (condition: SearchCondition) => void;
  updateCondition: (conditionId: string, updates: Partial<SearchCondition>) => void;
  removeCondition: (conditionId: string) => void;
  clearConditions: () => void;
  setConditions: (conditions: SearchCondition[]) => void;
  
  // 分页和排序
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
  setSort: (field: string, order: 'asc' | 'desc') => void;
  
  // 搜索执行
  performSearch: () => Promise<void>;
  immediateSearch: () => Promise<void>;
  setSearching: (isSearching: boolean) => void;
  
  // 历史记录
  addToHistory: (resultCount?: number, duration?: number) => void;
  loadFromHistory: (historyId: string) => void;
  clearHistory: () => void;
  
  // 模板管理
  saveAsTemplate: (name: string, description?: string, tags?: string[]) => void;
  loadTemplate: (templateId: string) => void;
  deleteTemplate: (templateId: string) => void;
  
  // URL 同步
  syncToUrl: (options?: { replace?: boolean }) => void;
  syncFromUrl: () => void;
  
  // 实用功能
  resetSearch: () => void;
  hasActiveSearch: boolean;
  searchSummary: string;
  activeFiltersCount: number;
  
  // UI 状态
  toggleHistory: () => void;
  toggleTemplates: () => void;

  // 性能相关
  getSearchMetrics: () => any;
  getPerformanceDiagnosis: () => any;

  // 错误处理相关
  getSearchLogs: () => any;
  clearSearchLogs: () => void;
  getHealthStatus: () => any;
  resetHealthStatus: () => void;
  showUserFeedback: {
    success: any;
    error: any;
    warning: any;
    info: any;
  };
}

export interface UseSearchManagerOptions {
  // 数据库标识
  database: string;
  
  // 是否自动同步到 URL
  autoSyncUrl?: boolean;
  
  // 是否在组件挂载时从 URL 加载状态
  loadFromUrl?: boolean;
  
  // 搜索回调函数
  onSearch?: (searchParams: {
    keyword: string;
    conditions: SearchCondition[];
    filters: SearchFilters;
    page: number;
    limit: number;
    sortBy: string;
    sortOrder: 'asc' | 'desc';
  }) => Promise<{ success: boolean; data?: any; total?: number }>;
  
  // 搜索状态变更回调
  onSearchStateChange?: (hasActiveSearch: boolean) => void;
  
  // 防抖延迟 (毫秒)
  debounceDelay?: number;
}

/**
 * 搜索条件管理主 Hook
 */
export function useSearchManager(options: UseSearchManagerOptions): UseSearchManagerReturn {
  const {
    database,
    autoSyncUrl = true,
    loadFromUrl = true,
    onSearch,
    onSearchStateChange,
    debounceDelay = 300,
  } = options;
  
  const _router = useRouter();
  const searchParams = useSearchParams();
  
  // Zustand store
  const searchState = useSearchStore();
  
  // 防抖定时器
  const debounceTimerRef = useRef<NodeJS.Timeout>();
  const lastSyncedStateRef = useRef<string>('');
  
  // 初始化数据库
  useEffect(() => {
    if (searchState.currentDatabase !== database) {
      searchState.setDatabase(database);
    }
  }, [database, searchState.currentDatabase, searchState.setDatabase]);
  
  // 从 URL 加载初始搜索状态 - 只在初次加载时执行
  useEffect(() => {
    if (!loadFromUrl) return;
    
    const urlState = urlParamsToSearchState(searchParams);
    if (Object.keys(urlState).length > 0) {
      // 更新 Zustand store
      if (urlState.keyword !== undefined) {
        searchState.setKeyword(urlState.keyword);
      }
      if (urlState.conditions) {
        searchState.setConditions(urlState.conditions);
      }
      if (urlState.filters) {
        searchState.setFilters(urlState.filters);
      }
      if (urlState.pagination) {
        searchState.setPagination(urlState.pagination);
      }
      if (urlState.sort) {
        searchState.setSort(urlState.sort);
      }
    }
  }, [searchParams, loadFromUrl, searchState.setKeyword, searchState.setConditions, searchState.setFilters, searchState.setPagination, searchState.setSort]);
  
  // 监听搜索状态变化，自动同步到 URL
  useEffect(() => {
    if (!autoSyncUrl) return;
    
    const currentStateKey = JSON.stringify({
      keyword: searchState.keyword,
      conditions: searchState.conditions,
      filters: searchState.filters,
      pagination: searchState.pagination,
      sort: searchState.sort,
    });
    
    // 避免无意义的重复同步
    if (currentStateKey === lastSyncedStateRef.current) {
      return;
    }
    
    lastSyncedStateRef.current = currentStateKey;
    
    // 清除之前的定时器
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    
    // 防抖更新 URL
    debounceTimerRef.current = setTimeout(() => {
      updateBrowserUrl({
        keyword: searchState.keyword,
        conditions: searchState.conditions,
        filters: searchState.filters,
        pagination: searchState.pagination,
        sort: searchState.sort,
        currentDatabase: searchState.currentDatabase,
      }, { replace: searchState.pagination.page === 1 });
    }, debounceDelay);
    
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [
    searchState.keyword,
    searchState.conditions,
    searchState.filters,
    searchState.pagination,
    searchState.sort,
    autoSyncUrl,
    debounceDelay,
  ]);
  
  // 监听搜索状态变化，通知父组件
  useEffect(() => {
    if (onSearchStateChange) {
      const hasActiveSearch = !!(searchState.keyword || searchState.conditions.length > 0 || Object.keys(searchState.filters).length > 0);
      onSearchStateChange(hasActiveSearch);
    }
  }, [
    searchState.keyword,
    searchState.conditions,
    searchState.filters,
    onSearchStateChange,
  ]);
  
  // 性能优化的搜索函数
  const { search: optimizedSearch, immediateSearch, getMetrics } = useSearchPerformance(
    async (searchParams: {
      keyword: string;
      conditions: SearchCondition[];
      filters: SearchFilters;
      page: number;
      limit: number;
      sortBy: string;
      sortOrder: 'asc' | 'desc';
      database?: string;
    }) => {
      if (!onSearch) throw new Error('onSearch function not provided');
      
      return await onSearch({
        keyword: searchParams.keyword,
        conditions: searchParams.conditions,
        filters: searchParams.filters,
        page: searchParams.page,
        limit: searchParams.limit,
        sortBy: searchParams.sortBy,
        sortOrder: searchParams.sortOrder,
      });
    },
    {
      enableCache: true,
      enableDeduplication: true,
      enableMetrics: true,
      enablePreload: false, // 可以根据需要开启
      debounceDelay: debounceDelay,
      cacheTimeout: 5 * 60 * 1000, // 5分钟缓存
    }
  );
  
  // 搜索执行函数（带错误处理）
  const performSearch = useCallback(async () => {
    if (!onSearch) return;
    
    const requestId = `search-${Date.now()}`;
    searchState.setSearching(true);
    const startTime = Date.now();
    
    // 记录搜索开始
    searchLogger.log('search', '开始搜索', {
      database: searchState.currentDatabase,
      keyword: searchState.keyword,
      conditionsCount: searchState.conditions.length,
      filtersCount: Object.keys(searchState.filters).length,
    });
    
    try {
      const result = await searchRetryManager.retry(
        requestId,
        async () => {
          return await optimizedSearch({
            keyword: searchState.keyword,
            conditions: searchState.conditions,
            filters: searchState.filters,
            page: searchState.pagination.page,
            limit: searchState.pagination.limit,
            sortBy: searchState.sort.field,
            sortOrder: searchState.sort.order,
            database: searchState.currentDatabase,
          });
        },
        (attempt, error) => {
          searchLogger.log('retry', `第 ${attempt} 次重试`, { error });
          userFeedback.showWarning(`搜索失败，正在进行第 ${attempt} 次重试...`);
        }
      );
      
      const duration = Date.now() - startTime;
      
      // 记录成功搜索
      searchHealthChecker.recordSearch(true, duration);
      searchLogger.log('success', '搜索成功', {
        resultCount: result?.total || 0,
        duration
      });

      if (result?.success) {
        searchState.setLastSearchResult(result.total || 0, duration);
        
        // 显示成功消息（仅在结果为空时）
        if ((result.total || 0) === 0) {
          userFeedback.showInfo('搜索完成', '未找到匹配的结果，请尝试调整搜索条件');
        }
        
        // 自动添加到历史记录
        searchState.addToHistory({
          keyword: searchState.keyword,
          conditions: searchState.conditions,
          filters: searchState.filters,
          database: searchState.currentDatabase,
          resultCount: result.total,
          searchDuration: duration,
        });
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      const searchError = classifyError(error);
      
      // 记录错误
      searchHealthChecker.recordSearch(false, duration);
      searchLogger.log('error', '搜索失败', { error: searchError });
      
      // 尝试错误恢复
      const recoveryResult = await SearchErrorRecovery.recoverFromError(searchError, {
        searchParams: {
          keyword: searchState.keyword,
          conditions: searchState.conditions,
          filters: searchState.filters,
          database: searchState.currentDatabase,
        },
        retryFunction: async () => {
          return await optimizedSearch({
            keyword: searchState.keyword,
            conditions: searchState.conditions,
            filters: searchState.filters,
            page: searchState.pagination.page,
            limit: searchState.pagination.limit,
            sortBy: searchState.sort.field,
            sortOrder: searchState.sort.order,
            database: searchState.currentDatabase,
          });
        },
        // 可以添加备用搜索方案
      });
      
      if (recoveryResult.success) {
        searchLogger.log('success', '错误恢复成功', { 
          fallbackUsed: recoveryResult.fallbackUsed 
        });
      }
    } finally {
      searchState.setSearching(false);
    }
  }, [optimizedSearch, searchState.keyword, searchState.conditions, searchState.filters, searchState.pagination, searchState.sort, searchState.currentDatabase, searchState.setSearching, searchState.setLastSearchResult, searchState.addToHistory]);
  
  // 包装的搜索操作函数
  const setKeyword = useCallback((keyword: string) => {
    searchState.setKeyword(keyword);
  }, [searchState.setKeyword]);
  
  const setFilters = useCallback((filters: SearchFilters) => {
    searchState.setFilters(filters);
  }, [searchState.setFilters]);
  
  const updateFilter = useCallback((key: string, value: unknown) => {
    searchState.updateFilter(key, value);
  }, [searchState.updateFilter]);
  
  const clearFilters = useCallback(() => {
    searchState.clearFilters();
  }, [searchState.clearFilters]);
  
  const addCondition = useCallback((condition: SearchCondition) => {
    searchState.addCondition(condition);
  }, [searchState.addCondition]);
  
  const updateCondition = useCallback((conditionId: string, updates: Partial<SearchCondition>) => {
    searchState.updateCondition(conditionId, updates);
  }, [searchState.updateCondition]);
  
  const removeCondition = useCallback((conditionId: string) => {
    searchState.removeCondition(conditionId);
  }, [searchState.removeCondition]);
  
  const clearConditions = useCallback(() => {
    searchState.clearConditions();
  }, [searchState.clearConditions]);
  
  const setConditions = useCallback((conditions: SearchCondition[]) => {
    searchState.setConditions(conditions);
  }, [searchState.setConditions]);
  
  const setPage = useCallback((page: number) => {
    searchState.setPagination({ page });
  }, [searchState.setPagination]);
  
  const setLimit = useCallback((limit: number) => {
    searchState.setPagination({ limit });
  }, [searchState.setPagination]);
  
  const setSort = useCallback((field: string, order: 'asc' | 'desc') => {
    searchState.setSort({ field, order });
  }, [searchState.setSort]);
  
  const setSearching = useCallback((isSearching: boolean) => {
    searchState.setSearching(isSearching);
  }, [searchState.setSearching]);
  
  const addToHistory = useCallback((resultCount?: number, duration?: number) => {
    searchState.addToHistory({
      keyword: searchState.keyword,
      conditions: searchState.conditions,
      filters: searchState.filters,
      database: searchState.currentDatabase,
      resultCount,
      searchDuration: duration,
    });
  }, [searchState.addToHistory, searchState.keyword, searchState.conditions, searchState.filters, searchState.currentDatabase]);
  
  const loadFromHistory = useCallback((historyId: string) => {
    searchState.loadFromHistory(historyId);
  }, [searchState.loadFromHistory]);
  
  const clearHistory = useCallback(() => {
    searchState.clearHistory();
  }, [searchState.clearHistory]);
  
  const saveAsTemplate = useCallback((name: string, description?: string, tags?: string[]) => {
    searchState.saveAsTemplate(name, description, false, tags);
  }, [searchState.saveAsTemplate]);
  
  const loadTemplate = useCallback((templateId: string) => {
    searchState.loadTemplate(templateId);
  }, [searchState.loadTemplate]);
  
  const deleteTemplate = useCallback((templateId: string) => {
    searchState.deleteTemplate(templateId);
  }, [searchState.deleteTemplate]);
  
  const syncToUrl = useCallback((options: { replace?: boolean } = {}) => {
    updateBrowserUrl({
      keyword: searchState.keyword,
      conditions: searchState.conditions,
      filters: searchState.filters,
      pagination: searchState.pagination,
      sort: searchState.sort,
      currentDatabase: searchState.currentDatabase,
    }, options);
  }, [searchState.keyword, searchState.conditions, searchState.filters, searchState.pagination, searchState.sort, searchState.currentDatabase]);
  
  const syncFromUrl = useCallback(() => {
    const urlState = extractSearchStateFromCurrentUrl();
    if (Object.keys(urlState).length > 0) {
      searchState.syncFromUrl(new URLSearchParams(window.location.search));
    }
  }, [searchState.syncFromUrl]);
  
  const resetSearch = useCallback(() => {
    searchState.resetToDatabase(database);
  }, [searchState.resetToDatabase, database]);
  
  const toggleHistory = useCallback(() => {
    searchState.toggleHistory();
  }, [searchState.toggleHistory]);
  
  const toggleTemplates = useCallback(() => {
    searchState.toggleTemplates();
  }, [searchState.toggleTemplates]);
  
  // 计算派生状态
  const hasActiveSearch = searchState.hasActiveSearch();
  const searchSummary = searchState.getCurrentSearchSummary();
  const activeFiltersCount = searchState.getActiveFiltersCount();
  
  return {
    searchState,
    
    // 基础搜索操作
    setKeyword,
    setFilters,
    updateFilter,
    clearFilters,
    
    // 搜索条件操作
    addCondition,
    updateCondition,
    removeCondition,
    clearConditions,
    setConditions,
    
    // 分页和排序
    setPage,
    setLimit,
    setSort,
    
    // 搜索执行
    performSearch,
    immediateSearch: useCallback(async () => {
      // 立即搜索，跳过防抖
      if (!onSearch) return;
      
      searchState.setSearching(true);
      try {
        const result = await immediateSearch({
          keyword: searchState.keyword,
          conditions: searchState.conditions,
          filters: searchState.filters,
          page: searchState.pagination.page,
          limit: searchState.pagination.limit,
          sortBy: searchState.sort.field,
          sortOrder: searchState.sort.order,
          database: searchState.currentDatabase,
        });
        
        if (result.success) {
          searchState.setLastSearchResult(result.total || 0, 0);
        }
      } catch (error) {
        console.error('Immediate search failed:', error);
      } finally {
        searchState.setSearching(false);
      }
    }, [immediateSearch, searchState.setSearching, searchState.setLastSearchResult, searchState.keyword, searchState.conditions, searchState.filters, searchState.pagination, searchState.sort, searchState.currentDatabase, onSearch]),
    setSearching,
    
    // 历史记录
    addToHistory,
    loadFromHistory,
    clearHistory,
    
    // 模板管理
    saveAsTemplate,
    loadTemplate,
    deleteTemplate,
    
    // URL 同步
    syncToUrl,
    syncFromUrl,
    
    // 实用功能
    resetSearch,
    hasActiveSearch,
    searchSummary,
    activeFiltersCount,
    
    // UI 状态
    toggleHistory,
    toggleTemplates,
    
    // 性能相关
    getSearchMetrics: getMetrics,
    getPerformanceDiagnosis: diagnoseSearchPerformance,
    
    // 错误处理相关
    getSearchLogs: () => searchLogger.getLogs(),
    clearSearchLogs: () => searchLogger.clear(),
    getHealthStatus: () => searchHealthChecker.getHealthStatus(),
    resetHealthStatus: () => searchHealthChecker.reset(),
    showUserFeedback: {
      success: userFeedback.showSuccess,
      error: userFeedback.showError,
      warning: userFeedback.showWarning,
      info: userFeedback.showInfo,
    },
  };
}

/**
 * 轻量级搜索状态 Hook (只读)
 * 适用于只需要读取搜索状态的组件
 */
export function useSearchState() {
  const searchState = useSearchStore();
  
  return {
    keyword: searchState.keyword,
    conditions: searchState.conditions,
    filters: searchState.filters,
    pagination: searchState.pagination,
    sort: searchState.sort,
    isSearching: searchState.isSearching,
    hasActiveSearch: searchState.hasActiveSearch(),
    searchSummary: searchState.getCurrentSearchSummary(),
    activeFiltersCount: searchState.getActiveFiltersCount(),
    history: searchState.history,
    templates: searchState.templates,
    showHistory: searchState.showHistory,
    showTemplates: searchState.showTemplates,
  };
}

/**
 * 搜索历史 Hook
 * 专门用于管理搜索历史的轻量级 Hook
 */
export function useSearchHistory() {
  const { history, addToHistory, loadFromHistory, clearHistory, removeHistoryItem } = useSearchStore();
  
  return {
    history,
    addToHistory: useCallback((searchParams: {
      keyword: string;
      conditions: SearchCondition[];
      filters: SearchFilters;
      database: string;
      resultCount?: number;
      searchDuration?: number;
    }) => {
      addToHistory(searchParams);
    }, [addToHistory]),
    loadFromHistory,
    clearHistory,
    removeHistoryItem,
  };
}

/**
 * 搜索模板 Hook
 * 专门用于管理搜索模板的轻量级 Hook
 */
export function useSearchTemplates() {
  const { 
    templates, 
    saveAsTemplate, 
    loadTemplate, 
    updateTemplate, 
    deleteTemplate 
  } = useSearchStore();
  
  return {
    templates,
    saveAsTemplate,
    loadTemplate,
    updateTemplate,
    deleteTemplate,
  };
}
