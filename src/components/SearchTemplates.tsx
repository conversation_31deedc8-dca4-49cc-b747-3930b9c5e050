/**
 * 搜索条件模板组件
 * 提供搜索模板的创建、管理、分享和快速应用功能
 */

"use client";

import React, { useState, useMemo } from 'react';
import {
  BookOpen,
  Plus,
  Edit2,
  Trash2,
  Share2,
  Filter,
  Clock,
  Tag,
  Users,
  TrendingUp,
  Copy,
  Check,

} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,

} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { SearchTemplate, SearchCondition, SearchFilters } from '@/types/search';

// 时间格式化
const formatTime = (timestamp: number): string => {
  const now = Date.now();
  const diff = now - timestamp;
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (days === 0) return '今天';
  if (days === 1) return '昨天';
  if (days < 7) return `${days}天前`;
  if (days < 30) return `${Math.floor(days / 7)}周前`;
  if (days < 365) return `${Math.floor(days / 30)}个月前`;
  
  return `${Math.floor(days / 365)}年前`;
};

// 生成模板摘要
const generateTemplateSummary = (template: SearchTemplate): string => {
  const parts: string[] = [];
  
  if (template.conditions.length > 0) {
    parts.push(`${template.conditions.length}个高级条件`);
  }
  
  const filterCount = Object.keys(template.filters).filter(key =>
    template.filters[key] !== undefined &&
    template.filters[key] !== null &&
    template.filters[key] !== ''
  ).length;
  
  if (filterCount > 0) {
    parts.push(`${filterCount}个筛选器`);
  }
  
  return parts.join(', ') || '无搜索条件';
};

// 模板卡片组件
interface TemplateCardProps {
  template: SearchTemplate;
  onLoad: (templateId: string) => void;
  onEdit?: (template: SearchTemplate) => void;
  onDelete?: (templateId: string) => void;
  onShare?: (template: SearchTemplate) => void;
  compact?: boolean;
}

const TemplateCard: React.FC<TemplateCardProps> = ({
  template,
  onLoad,
  onEdit,
  onDelete,
  onShare,
  compact = false,
}) => {
  const [copied, setCopied] = useState(false);
  
  const summary = generateTemplateSummary(template);
  const timeText = formatTime(template.updatedAt);
  
  const handleLoad = () => {
    onLoad(template.id);
  };
  
  const handleCopy = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await navigator.clipboard.writeText(JSON.stringify(template, null, 2));
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy template:', error);
    }
  };
  
  return (
    <div
      className={cn(
        "group border rounded-lg p-4 hover:bg-muted/50 transition-colors cursor-pointer",
        compact && "p-3"
      )}
      onClick={handleLoad}
    >
      <div className="flex items-start justify-between gap-3">
        {/* 左侧信息 */}
        <div className="flex-1 min-w-0">
          {/* 标题和标签 */}
          <div className="flex items-center gap-2 mb-2">
            <BookOpen className={cn("flex-shrink-0 text-blue-600", compact ? "h-4 w-4" : "h-5 w-5")} />
            <h4 className={cn("font-medium truncate", compact ? "text-sm" : "text-base")}>
              {template.name}
            </h4>
            
            {template.isPublic && (
              <Badge variant="secondary" className="text-xs">
                <Users className="h-3 w-3 mr-1" />
                公开
              </Badge>
            )}
            
            {template.usageCount > 0 && (
              <Badge variant="outline" className="text-xs">
                <TrendingUp className="h-3 w-3 mr-1" />
                {template.usageCount}次使用
              </Badge>
            )}
          </div>
          
          {/* 描述 */}
          {template.description && (
            <p className={cn("text-muted-foreground mb-2", compact ? "text-xs" : "text-sm")}>
              {template.description.length > 100 
                ? `${template.description.substring(0, 100)}...` 
                : template.description}
            </p>
          )}
          
          {/* 摘要信息 */}
          <div className="flex items-center gap-2 text-xs text-muted-foreground mb-2">
            <Filter className="h-3 w-3" />
            <span>{summary}</span>
            <Separator orientation="vertical" className="h-3" />
            <Clock className="h-3 w-3" />
            <span>更新于 {timeText}</span>
          </div>
          
          {/* 标签 */}
          {template.tags.length > 0 && (
            <div className="flex items-center gap-1 flex-wrap">
              {template.tags.slice(0, 3).map(tag => (
                <Badge key={tag} variant="outline" className="text-xs">
                  <Tag className="h-2 w-2 mr-1" />
                  {tag}
                </Badge>
              ))}
              {template.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{template.tags.length - 3}
                </Badge>
              )}
            </div>
          )}
        </div>
        
        {/* 右侧操作 */}
        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={handleCopy}
            title="复制模板"
          >
            {copied ? <Check className="h-3 w-3 text-green-600" /> : <Copy className="h-3 w-3" />}
          </Button>
          
          {onShare && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={(e) => {
                e.stopPropagation();
                onShare(template);
              }}
              title="分享模板"
            >
              <Share2 className="h-3 w-3" />
            </Button>
          )}
          
          {onEdit && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={(e) => {
                e.stopPropagation();
                onEdit(template);
              }}
              title="编辑模板"
            >
              <Edit2 className="h-3 w-3" />
            </Button>
          )}
          
          {onDelete && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 hover:text-red-600"
              onClick={(e) => {
                e.stopPropagation();
                onDelete(template.id);
              }}
              title="删除模板"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

// 创建/编辑模板对话框
interface TemplateDialogProps {
  template?: SearchTemplate;
  conditions: SearchCondition[];
  filters: SearchFilters;
  onSave: (name: string, description: string, tags: string[], isPublic: boolean) => void;
  onCancel: () => void;
  open: boolean;
}

const TemplateDialog: React.FC<TemplateDialogProps> = ({
  template,
  conditions,
  filters,
  onSave,
  onCancel,
  open,
}) => {
  const [name, setName] = useState(template?.name || '');
  const [description, setDescription] = useState(template?.description || '');
  const [tagsInput, setTagsInput] = useState(template?.tags.join(', ') || '');
  const [isPublic, setIsPublic] = useState(template?.isPublic || false);
  
  const tags = useMemo(() => {
    return tagsInput
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
  }, [tagsInput]);
  
  const handleSave = () => {
    if (!name.trim()) return;
    
    onSave(name.trim(), description.trim(), tags, isPublic);
    
    // 重置表单
    setName('');
    setDescription('');
    setTagsInput('');
    setIsPublic(false);
  };
  
  const handleCancel = () => {
    setName(template?.name || '');
    setDescription(template?.description || '');
    setTagsInput(template?.tags.join(', ') || '');
    setIsPublic(template?.isPublic || false);
    onCancel();
  };
  
  const hasConditions = conditions.length > 0 || Object.keys(filters).length > 0;
  
  return (
    <Dialog open={open} onOpenChange={(open) => !open && handleCancel()}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {template ? '编辑模板' : '保存为模板'}
          </DialogTitle>
          <DialogDescription>
            将当前搜索条件保存为模板，以便快速复用。
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* 模板名称 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">模板名称</label>
            <Input
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="为模板起个名字"
              maxLength={50}
            />
          </div>
          
          {/* 模板描述 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">描述 (可选)</label>
            <Textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="描述这个搜索模板的用途"
              rows={3}
              maxLength={200}
            />
          </div>
          
          {/* 标签 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">标签 (可选)</label>
            <Input
              value={tagsInput}
              onChange={(e) => setTagsInput(e.target.value)}
              placeholder="用逗号分隔多个标签"
            />
            {tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {tags.map(tag => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            )}
          </div>
          
          {/* 公开设置 */}
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="isPublic"
              checked={isPublic}
              onChange={(e) => setIsPublic(e.target.checked)}
              className="rounded"
            />
            <label htmlFor="isPublic" className="text-sm">
              设为公开模板 (其他用户可以使用)
            </label>
          </div>
          
          {/* 搜索条件预览 */}
          {hasConditions && (
            <div className="p-3 bg-muted rounded-lg">
              <h4 className="text-sm font-medium mb-2">包含的搜索条件:</h4>
              <div className="text-xs text-muted-foreground space-y-1">
                {conditions.length > 0 && (
                  <div>• {conditions.length} 个高级搜索条件</div>
                )}
                {Object.keys(filters).length > 0 && (
                  <div>• {Object.keys(filters).length} 个筛选器</div>
                )}
              </div>
            </div>
          )}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            取消
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={!name.trim() || !hasConditions}
          >
            {template ? '更新模板' : '保存模板'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// 主组件接口
export interface SearchTemplatesProps {
  // 模板数据
  templates: SearchTemplate[];
  
  // 当前搜索状态
  conditions: SearchCondition[];
  filters: SearchFilters;
  
  // 事件处理
  onLoadTemplate: (templateId: string) => void;
  onSaveTemplate: (name: string, description: string, tags: string[], isPublic: boolean) => void;
  onUpdateTemplate: (templateId: string, updates: Partial<SearchTemplate>) => void;
  onDeleteTemplate: (templateId: string) => void;
  
  // 显示配置
  maxItems?: number;
  compact?: boolean;
  showPublicTemplates?: boolean;
  
  // 过滤和排序
  sortBy?: 'name' | 'createdAt' | 'updatedAt' | 'usageCount';
  sortOrder?: 'asc' | 'desc';
  
  // 样式
  className?: string;
  emptyMessage?: string;
}

/**
 * 搜索模板主组件
 */
export default function SearchTemplates({
  templates = [],
  conditions = [],
  filters = {},
  onLoadTemplate,
  onSaveTemplate,
  onUpdateTemplate,
  onDeleteTemplate,
  maxItems = 20,
  compact = false,
  showPublicTemplates = true,
  sortBy = 'updatedAt',
  sortOrder = 'desc',
  className = '',
  emptyMessage = '暂无搜索模板',
}: SearchTemplatesProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTag, setSelectedTag] = useState<string>('all');
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<SearchTemplate | undefined>();
  const [currentSortBy, setCurrentSortBy] = useState(sortBy);
  const [currentSortOrder, setCurrentSortOrder] = useState(sortOrder);
  
  // 过滤和排序模板
  const filteredTemplates = useMemo(() => {
    let filtered = [...templates];
    
    // 按可见性过滤
    if (!showPublicTemplates) {
      filtered = filtered.filter(template => !template.isPublic);
    }
    
    // 按标签过滤
    if (selectedTag !== 'all') {
      filtered = filtered.filter(template => template.tags.includes(selectedTag));
    }
    
    // 按搜索词过滤
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(term) ||
        template.description?.toLowerCase().includes(term) ||
        template.tags.some(tag => tag.toLowerCase().includes(term))
      );
    }
    
    // 排序
    filtered.sort((a, b) => {
      let aValue: string | number, bValue: string | number;
      
      switch (currentSortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'createdAt':
          aValue = a.createdAt;
          bValue = b.createdAt;
          break;
        case 'usageCount':
          aValue = a.usageCount;
          bValue = b.usageCount;
          break;
        case 'updatedAt':
        default:
          aValue = a.updatedAt;
          bValue = b.updatedAt;
          break;
      }
      
      if (typeof aValue === 'string') {
        return currentSortOrder === 'asc' 
          ? aValue.localeCompare(bValue as string)
          : (bValue as string).localeCompare(aValue);
      }
      
      return currentSortOrder === 'asc' 
        ? (aValue as number) - (bValue as number) 
        : (bValue as number) - (aValue as number);
    });
    
    return filtered.slice(0, maxItems);
  }, [templates, searchTerm, selectedTag, showPublicTemplates, currentSortBy, currentSortOrder, maxItems]);
  
  // 获取所有标签
  const allTags = useMemo(() => {
    const tagSet = new Set<string>();
    templates.forEach(template => {
      template.tags.forEach(tag => tagSet.add(tag));
    });
    return Array.from(tagSet).sort();
  }, [templates]);
  
  // 分类模板
  const { myTemplates, publicTemplates } = useMemo(() => {
    return {
      myTemplates: filteredTemplates.filter(t => !t.isPublic),
      publicTemplates: filteredTemplates.filter(t => t.isPublic),
    };
  }, [filteredTemplates]);
  
  const hasCurrentSearch = conditions.length > 0 || Object.keys(filters).length > 0;
  
  const handleSaveTemplate = (name: string, description: string, tags: string[], isPublic: boolean) => {
    if (editingTemplate) {
      onUpdateTemplate(editingTemplate.id, {
        name,
        description,
        tags,
        isPublic,
      });
      setEditingTemplate(undefined);
    } else {
      onSaveTemplate(name, description, tags, isPublic);
    }
    setShowSaveDialog(false);
  };
  
  const handleEditTemplate = (template: SearchTemplate) => {
    setEditingTemplate(template);
    setShowSaveDialog(true);
  };
  
  const handleCancelDialog = () => {
    setShowSaveDialog(false);
    setEditingTemplate(undefined);
  };
  
  if (templates.length === 0) {
    return (
      <div className={cn("text-center py-8", className)}>
        <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-muted-foreground mb-4">{emptyMessage}</p>
        
        {hasCurrentSearch && (
          <Button onClick={() => setShowSaveDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            创建第一个模板
          </Button>
        )}
        
        <TemplateDialog
          conditions={conditions}
          filters={filters}
          onSave={handleSaveTemplate}
          onCancel={handleCancelDialog}
          open={showSaveDialog}
        />
      </div>
    );
  }
  
  return (
    <div className={cn("space-y-4", className)}>
      {/* 头部控制区域 */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-2">
          <BookOpen className="h-5 w-5 text-muted-foreground" />
          <h3 className="font-medium">搜索模板</h3>
          <Badge variant="secondary" className="text-xs">
            {filteredTemplates.length}
          </Badge>
        </div>
        
        <Button
          onClick={() => setShowSaveDialog(true)}
          disabled={!hasCurrentSearch}
          size="sm"
        >
          <Plus className="h-3 w-3 mr-1" />
          保存模板
        </Button>
      </div>
      
      {/* 搜索和过滤 */}
      <div className="flex items-center gap-2 flex-wrap">
        <Input
          placeholder="搜索模板..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="flex-1 min-w-40"
        />
        
        {allTags.length > 0 && (
          <Select value={selectedTag} onValueChange={setSelectedTag}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部标签</SelectItem>
              {allTags.map(tag => (
                <SelectItem key={tag} value={tag}>{tag}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              排序
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setCurrentSortBy('updatedAt')}>
              按更新时间
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setCurrentSortBy('createdAt')}>
              按创建时间
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setCurrentSortBy('name')}>
              按名称
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setCurrentSortBy('usageCount')}>
              按使用次数
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => setCurrentSortOrder(currentSortOrder === 'asc' ? 'desc' : 'asc')}>
              {currentSortOrder === 'asc' ? '降序' : '升序'}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      
      {/* 模板列表 */}
      <Tabs defaultValue="all" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="all">
            全部 ({filteredTemplates.length})
          </TabsTrigger>
          <TabsTrigger value="my">
            我的 ({myTemplates.length})
          </TabsTrigger>
          <TabsTrigger value="public">
            公开 ({publicTemplates.length})
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="all" className="space-y-2">
          {filteredTemplates.map(template => (
            <TemplateCard
              key={template.id}
              template={template}
              onLoad={onLoadTemplate}
              onEdit={handleEditTemplate}
              onDelete={onDeleteTemplate}
              compact={compact}
            />
          ))}
        </TabsContent>
        
        <TabsContent value="my" className="space-y-2">
          {myTemplates.map(template => (
            <TemplateCard
              key={template.id}
              template={template}
              onLoad={onLoadTemplate}
              onEdit={handleEditTemplate}
              onDelete={onDeleteTemplate}
              compact={compact}
            />
          ))}
        </TabsContent>
        
        <TabsContent value="public" className="space-y-2">
          {publicTemplates.map(template => (
            <TemplateCard
              key={template.id}
              template={template}
              onLoad={onLoadTemplate}
              compact={compact}
            />
          ))}
        </TabsContent>
      </Tabs>
      
      {/* 保存/编辑模板对话框 */}
      <TemplateDialog
        template={editingTemplate}
        conditions={conditions}
        filters={filters}
        onSave={handleSaveTemplate}
        onCancel={handleCancelDialog}
        open={showSaveDialog}
      />
    </div>
  );
}

