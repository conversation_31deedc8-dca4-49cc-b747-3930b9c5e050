/**
 * 搜索条件标签组件
 * 以标签形式展示当前搜索条件，支持编辑、删除和管理
 */

"use client";

import React, { useState, useMemo } from 'react';
import { X, Edit2, Search, Filter, Calendar, Hash, User, Building2, Package, MapPin, Clock, Plus } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { SearchCondition, SearchFilters } from '@/types/search';

// 字段图标映射
const FIELD_ICONS: Record<string, React.ComponentType<{ className?: string }>> = {
  // 基础字段
  'keyword': Search,
  'search': Search,
  'q': Search,
  
  // 身份信息
  'company': Building2,
  'manufacturer': Building2,
  'applicant': User,
  'agent': User,
  
  // 产品信息
  'product': Package,
  'device': Package,
  'model': Package,
  'brand': Package,
  
  // 时间字段
  'date': Calendar,
  'time': Clock,
  'created': Calendar,
  'updated': Calendar,
  'approved': Calendar,
  'submitted': Calendar,
  
  // 地理位置
  'country': MapPin,
  'region': MapPin,
  'location': MapPin,
  'address': MapPin,
  
  // 分类标识
  'category': Filter,
  'type': Filter,
  'class': Filter,
  'status': Filter,
  'level': Filter,
  
  // 数字编号
  'id': Hash,
  'number': Hash,
  'code': Hash,
  'registration': Hash,
  
  // 默认
  'default': Filter,
};

// 获取字段图标
const getFieldIcon = (fieldName: string): React.ComponentType<{ className?: string }> => {
  const lowercaseField = fieldName.toLowerCase();
  
  // 精确匹配
  if (FIELD_ICONS[lowercaseField]) {
    return FIELD_ICONS[lowercaseField];
  }
  
  // 模糊匹配
  for (const [key, icon] of Object.entries(FIELD_ICONS)) {
    if (lowercaseField.includes(key) || key.includes(lowercaseField)) {
      return icon;
    }
  }
  
  return FIELD_ICONS.default;
};

// 操作符显示名称映射
const OPERATOR_LABELS: Record<string, string> = {
  'contains': '包含',
  'equals': '等于',
  'startsWith': '开始于',
  'endsWith': '结束于',
  'notContains': '不包含',
  'notEquals': '不等于',
  'before': '早于',
  'after': '晚于',
  'between': '介于',
  'greaterThan': '大于',
  'lessThan': '小于',
  'greaterThanOrEqual': '大于等于',
  'lessThanOrEqual': '小于等于',
};

// 逻辑操作符显示
const LOGIC_LABELS: Record<string, string> = {
  'AND': '且',
  'OR': '或',
  'NOT': '非',
};

// 格式化条件值显示
const formatConditionValue = (value: string | { from?: string; to?: string }): string => {
  if (typeof value === 'string') {
    return value.length > 20 ? `${value.substring(0, 20)}...` : value;
  }
  
  if (typeof value === 'object' && value !== null) {
    if (value.from && value.to) {
      return `${value.from} ~ ${value.to}`;
    }
    if (value.from) {
      return `从 ${value.from}`;
    }
    if (value.to) {
      return `到 ${value.to}`;
    }
  }
  
  return '空值';
};

// 格式化过滤器值显示
const formatFilterValue = (value: unknown): string => {
  if (value === null || value === undefined) {
    return '空值';
  }
  
  if (Array.isArray(value)) {
    if (value.length === 0) return '空数组';
    if (value.length === 1) return String(value[0]);
    return `${value[0]} 等 ${value.length} 项`;
  }
  
  if (typeof value === 'object') {
    return '复杂对象';
  }
  
  const strValue = String(value);
  return strValue.length > 15 ? `${strValue.substring(0, 15)}...` : strValue;
};

// 生成条件描述文本
const generateConditionDescription = (condition: SearchCondition): string => {
  const operatorLabel = OPERATOR_LABELS[condition.operator] || condition.operator;
  const valueText = formatConditionValue(condition.value);
  
  return `${condition.field} ${operatorLabel} ${valueText}`;
};

// 单个条件标签组件
interface ConditionTagProps {
  condition: SearchCondition;
  showLogic?: boolean;
  onEdit?: (condition: SearchCondition) => void;
  onRemove?: (conditionId: string) => void;
  isEditable?: boolean;
  variant?: 'default' | 'secondary' | 'outline';
}

const ConditionTag: React.FC<ConditionTagProps> = ({
  condition,
  showLogic = true,
  onEdit,
  onRemove,
  isEditable = true,
  variant = 'default',
}) => {
  const IconComponent = getFieldIcon(condition.field);
  const description = generateConditionDescription(condition);
  
  return (
    <div className="flex items-center gap-1">
      {/* 逻辑操作符 */}
      {showLogic && condition.logic && (
        <Badge variant="outline" className="text-xs px-2 py-1">
          {LOGIC_LABELS[condition.logic]}
        </Badge>
      )}
      
      {/* 条件标签 */}
      <Badge variant={variant} className="flex items-center gap-1.5 pl-2 pr-1 py-1 max-w-xs">
        <IconComponent className="h-3 w-3 flex-shrink-0" />
        <span className="text-xs truncate" title={description}>
          {description}
        </span>
        
        {isEditable && (
          <div className="flex items-center gap-0.5 ml-1">
            {onEdit && (
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-white/20"
                onClick={() => onEdit(condition)}
                title="编辑条件"
              >
                <Edit2 className="h-3 w-3" />
              </Button>
            )}
            
            {onRemove && (
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-red-100 hover:text-red-600"
                onClick={() => onRemove(condition.id)}
                title="删除条件"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        )}
      </Badge>
    </div>
  );
};

// 单个过滤器标签组件
interface FilterTagProps {
  filterKey: string;
  filterValue: unknown;
  onEdit?: (key: string, value: unknown) => void;
  onRemove?: (key: string) => void;
  isEditable?: boolean;
  variant?: 'default' | 'secondary' | 'outline';
}

const FilterTag: React.FC<FilterTagProps> = ({
  filterKey,
  filterValue,
  onEdit,
  onRemove,
  isEditable = true,
  variant = 'secondary',
}) => {
  const IconComponent = getFieldIcon(filterKey);
  const valueText = formatFilterValue(filterValue);
  
  return (
    <Badge variant={variant} className="flex items-center gap-1.5 pl-2 pr-1 py-1 max-w-xs">
      <IconComponent className="h-3 w-3 flex-shrink-0" />
      <span className="text-xs truncate" title={`${filterKey}: ${valueText}`}>
        {filterKey}: {valueText}
      </span>
      
      {isEditable && (
        <div className="flex items-center gap-0.5 ml-1">
          {onEdit && (
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 hover:bg-white/20"
              onClick={() => onEdit(filterKey, filterValue)}
              title="编辑筛选器"
            >
              <Edit2 className="h-3 w-3" />
            </Button>
          )}
          
          {onRemove && (
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 hover:bg-red-100 hover:text-red-600"
              onClick={() => onRemove(filterKey)}
              title="删除筛选器"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      )}
    </Badge>
  );
};

// 关键词标签组件
interface KeywordTagProps {
  keyword: string;
  onEdit?: (keyword: string) => void;
  onRemove?: () => void;
  isEditable?: boolean;
}

const KeywordTag: React.FC<KeywordTagProps> = ({
  keyword,
  onEdit,
  onRemove,
  isEditable = true,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(keyword);
  
  const handleSave = () => {
    if (onEdit && editValue.trim()) {
      onEdit(editValue.trim());
    }
    setIsEditing(false);
  };
  
  const handleCancel = () => {
    setEditValue(keyword);
    setIsEditing(false);
  };
  
  if (isEditing) {
    return (
      <div className="flex items-center gap-1">
        <Input
          value={editValue}
          onChange={(e) => setEditValue(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') handleSave();
            if (e.key === 'Escape') handleCancel();
          }}
          className="h-7 w-40 text-xs"
          placeholder="输入关键词"
          autoFocus
        />
        <Button size="sm" onClick={handleSave} className="h-7 px-2">
          保存
        </Button>
        <Button size="sm" variant="outline" onClick={handleCancel} className="h-7 px-2">
          取消
        </Button>
      </div>
    );
  }
  
  return (
    <Badge variant="default" className="flex items-center gap-1.5 pl-2 pr-1 py-1">
      <Search className="h-3 w-3 flex-shrink-0" />
      <span className="text-xs font-medium" title={keyword}>
        {keyword.length > 20 ? `${keyword.substring(0, 20)}...` : keyword}
      </span>
      
      {isEditable && (
        <div className="flex items-center gap-0.5 ml-1">
          {onEdit && (
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 hover:bg-white/20"
              onClick={() => setIsEditing(true)}
              title="编辑关键词"
            >
              <Edit2 className="h-3 w-3" />
            </Button>
          )}
          
          {onRemove && (
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 hover:bg-red-100 hover:text-red-600"
              onClick={onRemove}
              title="删除关键词"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      )}
    </Badge>
  );
};

// 主组件接口
export interface SearchConditionTagsProps {
  // 搜索状态
  keyword?: string;
  conditions?: SearchCondition[];
  filters?: SearchFilters;
  
  // 事件处理
  onKeywordChange?: (keyword: string) => void;
  onKeywordRemove?: () => void;
  onConditionEdit?: (condition: SearchCondition) => void;
  onConditionRemove?: (conditionId: string) => void;
  onFilterEdit?: (key: string, value: unknown) => void;
  onFilterRemove?: (key: string) => void;
  onClearAll?: () => void;
  onAddCondition?: () => void;
  
  // 显示配置
  maxDisplayTags?: number;
  showEditButtons?: boolean;
  showAddButton?: boolean;
  showClearButton?: boolean;
  showLogicOperators?: boolean;
  
  // 样式配置
  className?: string;
  compact?: boolean;
  
  // 字段配置
  availableFields?: Array<{
    fieldName: string;
    displayName: string;
    fieldType: string;
  }>;
}

/**
 * 搜索条件标签主组件
 */
export default function SearchConditionTags({
  keyword = '',
  conditions = [],
  filters = {},
  onKeywordChange,
  onKeywordRemove,
  onConditionEdit,
  onConditionRemove,
  onFilterEdit,
  onFilterRemove,
  onClearAll,
  onAddCondition,
  maxDisplayTags = 10,
  showEditButtons = true,
  showAddButton = true,
  showClearButton = true,
  showLogicOperators = true,
  className = '',
  compact = false,
  availableFields: _availableFields = [],
}: SearchConditionTagsProps) {
  const [showAll, setShowAll] = useState(false);
  
  // 过滤器数据处理
  const activeFilters = useMemo(() => {
    return Object.entries(filters).filter(([_key, value]) =>
      value !== undefined && value !== null && value !== ''
    );
  }, [filters]);
  
  // 计算总标签数
  const totalTags = (keyword ? 1 : 0) + conditions.length + activeFilters.length;
  
  // 是否有搜索条件
  const hasSearchConditions = totalTags > 0;
  
  // 显示的标签数量
  const _displayedTags = showAll ? totalTags : Math.min(totalTags, maxDisplayTags);
  const hasMoreTags = totalTags > maxDisplayTags;
  
  // 渲染所有标签
  const renderTags = () => {
    const tags: React.ReactNode[] = [];
    let tagCount = 0;
    
    // 关键词标签
    if (keyword && (showAll || tagCount < maxDisplayTags)) {
      tags.push(
        <KeywordTag
          key="keyword"
          keyword={keyword}
          onEdit={onKeywordChange}
          onRemove={onKeywordRemove}
          isEditable={showEditButtons}
        />
      );
      tagCount++;
    }
    
    // 高级条件标签
    for (let i = 0; i < conditions.length && (showAll || tagCount < maxDisplayTags); i++) {
      const condition = conditions[i];
      tags.push(
        <ConditionTag
          key={condition.id}
          condition={condition}
          showLogic={showLogicOperators && i > 0}
          onEdit={onConditionEdit}
          onRemove={onConditionRemove}
          isEditable={showEditButtons}
        />
      );
      tagCount++;
    }
    
    // 过滤器标签
    for (let i = 0; i < activeFilters.length && (showAll || tagCount < maxDisplayTags); i++) {
      const [key, value] = activeFilters[i];
      tags.push(
        <FilterTag
          key={`filter-${key}`}
          filterKey={key}
          filterValue={value}
          onEdit={onFilterEdit}
          onRemove={onFilterRemove}
          isEditable={showEditButtons}
        />
      );
      tagCount++;
    }
    
    return tags;
  };
  
  if (!hasSearchConditions) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <div className="text-sm text-muted-foreground">
          暂无搜索条件
        </div>
        
        {showAddButton && onAddCondition && (
          <Button
            variant="outline"
            size="sm"
            onClick={onAddCondition}
            className="flex items-center gap-1 h-7"
          >
            <Plus className="h-3 w-3" />
            添加条件
          </Button>
        )}
      </div>
    );
  }
  
  return (
    <div className={cn("space-y-2", className)}>
      {/* 标签展示区域 */}
      <div className={cn(
        "flex flex-wrap items-center gap-1.5",
        compact && "gap-1"
      )}>
        {renderTags()}
        
        {/* 更多标签提示 */}
        {hasMoreTags && !showAll && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAll(true)}
            className="h-6 px-2 text-xs"
          >
            +{totalTags - maxDisplayTags} 更多
          </Button>
        )}
        
        {/* 收起按钮 */}
        {showAll && hasMoreTags && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAll(false)}
            className="h-6 px-2 text-xs"
          >
            收起
          </Button>
        )}
      </div>
      
      {/* 操作按钮区域 */}
      <div className="flex items-center gap-2">
        {showAddButton && onAddCondition && (
          <Button
            variant="outline"
            size="sm"
            onClick={onAddCondition}
            className="flex items-center gap-1 h-7"
          >
            <Plus className="h-3 w-3" />
            添加条件
          </Button>
        )}
        
        {showClearButton && onClearAll && hasSearchConditions && (
          <Button
            variant="outline"
            size="sm"
            onClick={onClearAll}
            className="h-7 text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            清空所有
          </Button>
        )}
        
        {/* 搜索统计 */}
        <div className="text-xs text-muted-foreground">
          共 {totalTags} 个搜索条件
        </div>
      </div>
    </div>
  );
}

