/**
 * 搜索历史组件
 * 提供搜索历史记录的展示、管理和快速访问功能
 */

"use client";

import React, { useState, useMemo } from 'react';
import {
  Clock,
  Search,
  Filter,
  Trash2,
  Star,
  Calendar,
  Database,
  TrendingUp,
  Eye,
  EyeOff,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { SearchHistoryItem } from '@/types/search';

// 时间格式化工具
const formatTime = (timestamp: number): string => {
  const now = Date.now();
  const diff = now - timestamp;
  
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;
  
  return new Date(timestamp).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// 格式化搜索时长
const formatDuration = (duration?: number): string => {
  if (!duration) return '';
  
  if (duration < 1000) {
    return `${duration}ms`;
  }
  
  return `${(duration / 1000).toFixed(1)}s`;
};

// 格式化结果数量
const formatResultCount = (count?: number): string => {
  if (count === undefined) return '';
  
  if (count < 1000) {
    return count.toString();
  }
  
  if (count < 10000) {
    return `${(count / 1000).toFixed(1)}k`;
  }
  
  return `${Math.floor(count / 1000)}k`;
};

// 生成搜索摘要
const generateSearchSummary = (item: SearchHistoryItem): string => {
  const parts: string[] = [];
  
  if (item.keyword) {
    parts.push(`关键词: "${item.keyword}"`);
  }
  
  if (item.conditions.length > 0) {
    parts.push(`${item.conditions.length}个高级条件`);
  }
  
  const filterCount = Object.keys(item.filters).filter(key =>
    item.filters[key] !== undefined &&
    item.filters[key] !== null &&
    item.filters[key] !== ''
  ).length;
  
  if (filterCount > 0) {
    parts.push(`${filterCount}个筛选器`);
  }
  
  return parts.join(', ') || '空搜索';
};

// 单个历史项组件
interface HistoryItemProps {
  item: SearchHistoryItem;
  onLoad: (historyId: string) => void;
  onRemove: (historyId: string) => void;
  onSaveAsTemplate?: (item: SearchHistoryItem) => void;
  showDatabase?: boolean;
  compact?: boolean;
}

const HistoryItem: React.FC<HistoryItemProps> = ({
  item,
  onLoad,
  onRemove,
  onSaveAsTemplate,
  showDatabase = true,
  compact = false,
}) => {
  const [showDetails, setShowDetails] = useState(false);
  
  const summary = generateSearchSummary(item);
  const timeText = formatTime(item.timestamp);
  const durationText = formatDuration(item.searchDuration);
  const resultText = formatResultCount(item.resultCount);
  
  const handleLoad = () => {
    onLoad(item.id);
  };
  
  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    onRemove(item.id);
  };
  
  const handleSaveAsTemplate = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onSaveAsTemplate) {
      onSaveAsTemplate(item);
    }
  };
  
  return (
    <div
      className={cn(
        "group border rounded-lg p-3 hover:bg-muted/50 transition-colors cursor-pointer",
        compact && "p-2"
      )}
      onClick={handleLoad}
    >
      <div className="flex items-start justify-between gap-2">
        {/* 左侧信息 */}
        <div className="flex-1 min-w-0">
          {/* 描述和时间 */}
          <div className="flex items-center gap-2 mb-1">
            <Search className={cn("flex-shrink-0 text-muted-foreground", compact ? "h-3 w-3" : "h-4 w-4")} />
            <span className={cn("font-medium truncate", compact ? "text-sm" : "text-base")}>
              {item.description || summary}
            </span>
            <Clock className="h-3 w-3 text-muted-foreground flex-shrink-0" />
            <span className="text-xs text-muted-foreground flex-shrink-0">
              {timeText}
            </span>
          </div>
          
          {/* 详细信息 */}
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            {showDatabase && (
              <>
                <Database className="h-3 w-3" />
                <span>{item.database}</span>
                <Separator orientation="vertical" className="h-3" />
              </>
            )}
            
            {item.resultCount !== undefined && (
              <>
                <TrendingUp className="h-3 w-3" />
                <span>{resultText} 条结果</span>
              </>
            )}
            
            {durationText && (
              <>
                <Separator orientation="vertical" className="h-3" />
                <span>{durationText}</span>
              </>
            )}
          </div>
          
          {/* 搜索条件预览 */}
          {!compact && (
            <div className="flex items-center gap-1 mt-2 flex-wrap">
              {item.keyword && (
                <Badge variant="outline" className="text-xs">
                  <Search className="h-2 w-2 mr-1" />
                  {item.keyword.length > 15 ? `${item.keyword.substring(0, 15)}...` : item.keyword}
                </Badge>
              )}
              
              {item.conditions.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  <Filter className="h-2 w-2 mr-1" />
                  {item.conditions.length} 个条件
                </Badge>
              )}
              
              {Object.keys(item.filters).length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {Object.keys(item.filters).length} 个筛选器
                </Badge>
              )}
            </div>
          )}
        </div>
        
        {/* 右侧操作 */}
        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          {onSaveAsTemplate && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={handleSaveAsTemplate}
              title="保存为模板"
            >
              <Star className="h-3 w-3" />
            </Button>
          )}
          
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => setShowDetails(!showDetails)}
            title={showDetails ? "隐藏详情" : "显示详情"}
          >
            {showDetails ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 hover:text-red-600"
            onClick={handleRemove}
            title="删除历史"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </div>
      
      {/* 详细条件展示 */}
      {showDetails && (
        <div className="mt-3 pt-3 border-t space-y-2 text-xs">
          {item.keyword && (
            <div>
              <span className="font-medium">关键词：</span>
              <span className="text-muted-foreground">{item.keyword}</span>
            </div>
          )}
          
          {item.conditions.length > 0 && (
            <div>
              <span className="font-medium">高级条件：</span>
              <div className="mt-1 space-y-1">
                {item.conditions.map((condition, index) => (
                  <div key={condition.id} className="text-muted-foreground">
                    {index > 0 && condition.logic && (
                      <span className="text-blue-600 mr-1">{condition.logic}</span>
                    )}
                    {condition.field} {condition.operator} {
                      typeof condition.value === 'string' 
                        ? condition.value 
                        : JSON.stringify(condition.value)
                    }
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {Object.keys(item.filters).length > 0 && (
            <div>
              <span className="font-medium">筛选器：</span>
              <div className="mt-1">
                {Object.entries(item.filters).map(([key, value]) => (
                  <span key={key} className="text-muted-foreground mr-2">
                    {key}: {Array.isArray(value) ? value.join(', ') : String(value)}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// 主组件接口
export interface SearchHistoryProps {
  // 历史数据
  history: SearchHistoryItem[];
  
  // 事件处理
  onLoadHistory: (historyId: string) => void;
  onRemoveHistory: (historyId: string) => void;
  onClearHistory: () => void;
  onSaveAsTemplate?: (item: SearchHistoryItem) => void;
  
  // 显示配置
  maxItems?: number;
  showDatabase?: boolean;
  compact?: boolean;
  groupByDate?: boolean;
  
  // 过滤和排序
  filterByDatabase?: string;
  sortBy?: 'timestamp' | 'resultCount' | 'searchDuration';
  sortOrder?: 'asc' | 'desc';
  
  // 样式
  className?: string;
  emptyMessage?: string;
}

/**
 * 搜索历史主组件
 */
export default function SearchHistory({
  history = [],
  onLoadHistory,
  onRemoveHistory,
  onClearHistory,
  onSaveAsTemplate,
  maxItems = 20,
  showDatabase = true,
  compact = false,
  groupByDate = false,
  filterByDatabase,
  sortBy = 'timestamp',
  sortOrder = 'desc',
  className = '',
  emptyMessage = '暂无搜索历史',
}: SearchHistoryProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [showClearDialog, setShowClearDialog] = useState(false);
  const [selectedDatabase, setSelectedDatabase] = useState<string>(filterByDatabase || 'all');
  const [currentSortBy, setCurrentSortBy] = useState(sortBy);
  const [currentSortOrder, setCurrentSortOrder] = useState(sortOrder);
  
  // 过滤和排序历史记录
  const filteredHistory = useMemo(() => {
    let filtered = [...history];
    
    // 按数据库过滤
    if (selectedDatabase !== 'all') {
      filtered = filtered.filter(item => item.database === selectedDatabase);
    }
    
    // 按搜索词过滤
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(item =>
        item.keyword.toLowerCase().includes(term) ||
        item.description.toLowerCase().includes(term) ||
        item.database.toLowerCase().includes(term)
      );
    }
    
    // 排序
    filtered.sort((a, b) => {
      let aValue: number, bValue: number;
      
      switch (currentSortBy) {
        case 'resultCount':
          aValue = a.resultCount || 0;
          bValue = b.resultCount || 0;
          break;
        case 'searchDuration':
          aValue = a.searchDuration || 0;
          bValue = b.searchDuration || 0;
          break;
        case 'timestamp':
        default:
          aValue = a.timestamp;
          bValue = b.timestamp;
          break;
      }
      
      return currentSortOrder === 'asc' ? aValue - bValue : bValue - aValue;
    });
    
    // 限制数量
    return filtered.slice(0, maxItems);
  }, [history, selectedDatabase, searchTerm, currentSortBy, currentSortOrder, maxItems]);
  
  // 获取可用的数据库列表
  const availableDatabases = useMemo(() => {
    const databases = new Set(history.map(item => item.database));
    return Array.from(databases).sort();
  }, [history]);
  
  // 按日期分组
  const groupedHistory = useMemo(() => {
    if (!groupByDate) return null;
    
    const groups: Record<string, SearchHistoryItem[]> = {};
    
    filteredHistory.forEach(item => {
      const date = new Date(item.timestamp);
      const dateKey = date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(item);
    });
    
    return groups;
  }, [filteredHistory, groupByDate]);
  
  const handleClearHistory = () => {
    onClearHistory();
    setShowClearDialog(false);
  };
  
  if (history.length === 0) {
    return (
      <div className={cn("text-center py-8", className)}>
        <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-muted-foreground">{emptyMessage}</p>
      </div>
    );
  }
  
  return (
    <div className={cn("space-y-4", className)}>
      {/* 头部控制区域 */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-2">
          <Clock className="h-5 w-5 text-muted-foreground" />
          <h3 className="font-medium">搜索历史</h3>
          <Badge variant="secondary" className="text-xs">
            {filteredHistory.length}
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          {/* 清空历史按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowClearDialog(true)}
            className="text-red-600 hover:text-red-700"
            disabled={history.length === 0}
          >
            <Trash2 className="h-3 w-3 mr-1" />
            清空
          </Button>
        </div>
      </div>
      
      {/* 搜索和过滤 */}
      <div className="flex items-center gap-2 flex-wrap">
        <Input
          placeholder="搜索历史记录..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="flex-1 min-w-40"
        />
        
        {availableDatabases.length > 1 && (
          <Select value={selectedDatabase} onValueChange={setSelectedDatabase}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部数据库</SelectItem>
              {availableDatabases.map(db => (
                <SelectItem key={db} value={db}>{db}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              排序
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setCurrentSortBy('timestamp')}>
              按时间排序
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setCurrentSortBy('resultCount')}>
              按结果数排序
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setCurrentSortBy('searchDuration')}>
              按搜索时长排序
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => setCurrentSortOrder(currentSortOrder === 'asc' ? 'desc' : 'asc')}>
              {currentSortOrder === 'asc' ? '降序' : '升序'}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      
      {/* 历史记录列表 */}
      <div className="space-y-2">
        {groupedHistory ? (
          // 按日期分组显示
          Object.entries(groupedHistory).map(([date, items]) => (
            <div key={date} className="space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                <Calendar className="h-4 w-4" />
                {date}
              </div>
              <div className="space-y-2 ml-6">
                {items.map(item => (
                  <HistoryItem
                    key={item.id}
                    item={item}
                    onLoad={onLoadHistory}
                    onRemove={onRemoveHistory}
                    onSaveAsTemplate={onSaveAsTemplate}
                    showDatabase={showDatabase}
                    compact={compact}
                  />
                ))}
              </div>
            </div>
          ))
        ) : (
          // 普通列表显示
          filteredHistory.map(item => (
            <HistoryItem
              key={item.id}
              item={item}
              onLoad={onLoadHistory}
              onRemove={onRemoveHistory}
              onSaveAsTemplate={onSaveAsTemplate}
              showDatabase={showDatabase}
              compact={compact}
            />
          ))
        )}
      </div>
      
      {/* 加载更多按钮 */}
      {filteredHistory.length === maxItems && history.length > maxItems && (
        <div className="text-center">
          <Button variant="outline" size="sm">
            加载更多历史记录
          </Button>
        </div>
      )}
      
      {/* 清空确认对话框 */}
      <Dialog open={showClearDialog} onOpenChange={setShowClearDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>清空搜索历史</DialogTitle>
            <DialogDescription>
              确定要清空所有搜索历史记录吗？此操作不可撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowClearDialog(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleClearHistory}>
              确定清空
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

