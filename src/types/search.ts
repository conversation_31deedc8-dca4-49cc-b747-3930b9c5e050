/**
 * 搜索条件管理系统的类型定义
 * 扩展现有的 SearchCondition 接口，添加完整的搜索状态管理
 */

// 重新导出现有的 SearchCondition 类型
export interface SearchCondition {
  id: string;
  field: string;
  operator: string;
  value: string | { from?: string; to?: string };
  logic?: 'AND' | 'OR' | 'NOT';
}

// 搜索过滤器类型
export interface SearchFilters {
  [key: string]: unknown;
  // 常用过滤器
  keyword?: string;
  category?: string | string[];
  dateRange?: {
    from?: string;
    to?: string;
  };
  status?: string | string[];
}

// 分页信息
export interface PaginationState {
  page: number;
  limit: number;
  total?: number;
}

// 排序信息
export interface SortState {
  field: string;
  order: 'asc' | 'desc';
}

// 搜索历史项
export interface SearchHistoryItem {
  id: string;
  timestamp: number;
  conditions: SearchCondition[];
  filters: SearchFilters;
  keyword: string;
  database: string;
  resultCount?: number;
  searchDuration?: number;
  // 用于显示的搜索描述
  description: string;
}

// 搜索模板
export interface SearchTemplate {
  id: string;
  name: string;
  description?: string;
  conditions: SearchCondition[];
  filters: SearchFilters;
  isPublic: boolean;
  createdAt: number;
  updatedAt: number;
  usageCount: number;
  tags: string[];
}

// 搜索状态
export interface SearchState {
  // 当前数据库
  currentDatabase: string;
  
  // 搜索条件
  keyword: string;
  conditions: SearchCondition[];
  filters: SearchFilters;
  
  // 分页和排序
  pagination: PaginationState;
  sort: SortState;
  
  // 搜索状态
  isSearching: boolean;
  lastSearchTime?: number;
  resultCount?: number;
  
  // 历史记录 (最近20次)
  history: SearchHistoryItem[];
  
  // 保存的模板 (最多50个)
  templates: SearchTemplate[];
  
  // UI 状态
  showHistory: boolean;
  showTemplates: boolean;
  expandedConditions: Set<string>;
}

// 搜索操作类型
export type SearchActionType = 
  | 'SET_KEYWORD'
  | 'SET_CONDITIONS'
  | 'ADD_CONDITION'
  | 'UPDATE_CONDITION'
  | 'REMOVE_CONDITION'
  | 'CLEAR_CONDITIONS'
  | 'SET_FILTERS'
  | 'UPDATE_FILTER'
  | 'CLEAR_FILTERS'
  | 'SET_PAGINATION'
  | 'SET_SORT'
  | 'SET_SEARCHING'
  | 'ADD_TO_HISTORY'
  | 'CLEAR_HISTORY'
  | 'SAVE_TEMPLATE'
  | 'LOAD_TEMPLATE'
  | 'DELETE_TEMPLATE'
  | 'TOGGLE_HISTORY'
  | 'TOGGLE_TEMPLATES'
  | 'SYNC_FROM_URL'
  | 'RESET_SEARCH';

// URL 同步参数
export interface URLSearchParams {
  q?: string;              // 关键词
  page?: number;           // 页码
  limit?: number;          // 每页数量
  sortBy?: string;         // 排序字段
  sortOrder?: 'asc' | 'desc'; // 排序方向
  filters?: string;        // JSON序列化的过滤器
  conditions?: string;     // JSON序列化的高级条件
}

// 搜索结果元数据
export interface SearchResultMeta {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  searchTime: number;
  hasMore: boolean;
  aggregations?: Record<string, unknown>;
}

// 搜索建议
export interface SearchSuggestion {
  type: 'keyword' | 'filter' | 'condition' | 'template';
  value: string;
  label: string;
  count?: number;
  description?: string;
}

// 搜索配置
export interface SearchConfig {
  // 历史记录配置
  maxHistoryItems: number;
  historyTTL: number; // 历史记录保存时长(毫秒)
  
  // 模板配置
  maxTemplates: number;
  allowPublicTemplates: boolean;
  
  // URL 同步配置
  syncToURL: boolean;
  compressURL: boolean;
  
  // 性能配置
  debounceDelay: number;
  enableCache: boolean;
  cacheTimeout: number;
  
  // 功能开关
  enableHistory: boolean;
  enableTemplates: boolean;
  enableSuggestions: boolean;
  enableAutoSave: boolean;
}

// 搜索事件
export interface SearchEvent {
  type: 'search' | 'filter' | 'sort' | 'paginate' | 'clear' | 'load_template' | 'save_template';
  timestamp: number;
  database: string;
  searchParams: {
    keyword?: string;
    conditionsCount: number;
    filtersCount: number;
    sortField?: string;
  };
  resultCount?: number;
  duration?: number;
}

// 导出类型守卫函数
export function isSearchCondition(obj: unknown): obj is SearchCondition {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'field' in obj &&
    'operator' in obj &&
    'value' in obj
  );
}

export function isSearchTemplate(obj: unknown): obj is SearchTemplate {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'name' in obj &&
    'conditions' in obj &&
    'filters' in obj
  );
}

// 默认配置
export const DEFAULT_SEARCH_CONFIG: SearchConfig = {
  maxHistoryItems: 20,
  historyTTL: 7 * 24 * 60 * 60 * 1000, // 7天
  maxTemplates: 50,
  allowPublicTemplates: true,
  syncToURL: true,
  compressURL: true,
  debounceDelay: 300,
  enableCache: true,
  cacheTimeout: 5 * 60 * 1000, // 5分钟
  enableHistory: true,
  enableTemplates: true,
  enableSuggestions: true,
  enableAutoSave: false,
};

// 默认搜索状态
export const DEFAULT_SEARCH_STATE: SearchState = {
  currentDatabase: '',
  keyword: '',
  conditions: [],
  filters: {},
  pagination: {
    page: 1,
    limit: 20,
  },
  sort: {
    field: '_score',
    order: 'desc',
  },
  isSearching: false,
  history: [],
  templates: [],
  showHistory: false,
  showTemplates: false,
  expandedConditions: new Set(),
};



