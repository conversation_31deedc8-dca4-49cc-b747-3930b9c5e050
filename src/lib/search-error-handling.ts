/**
 * 搜索错误处理和用户反馈机制
 * 提供统一的错误处理、用户友好的错误信息和重试机制
 */

import { toast } from '@/hooks/use-toast';

// 错误类型枚举
export enum SearchErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  CACHE_ERROR = 'CACHE_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

// 搜索错误接口
export interface SearchError {
  type: SearchErrorType;
  message: string;
  originalError?: Error;
  code?: string | number;
  retryable?: boolean;
  timestamp: number;
  context?: Record<string, unknown>;
}

// 错误消息映射
const ERROR_MESSAGES: Record<SearchErrorType, string> = {
  [SearchErrorType.NETWORK_ERROR]: '网络连接失败，请检查网络设置后重试',
  [SearchErrorType.VALIDATION_ERROR]: '搜索参数有误，请检查搜索条件',
  [SearchErrorType.AUTHORIZATION_ERROR]: '权限不足，请登录后重试',
  [SearchErrorType.SERVER_ERROR]: '服务器暂时无法响应，请稍后重试',
  [SearchErrorType.TIMEOUT_ERROR]: '搜索请求超时，请稍后重试',
  [SearchErrorType.CACHE_ERROR]: '缓存数据异常，已为您重新搜索',
  [SearchErrorType.UNKNOWN_ERROR]: '搜索过程中出现未知错误，请重试',
};

// 错误分类函数
export function classifyError(error: unknown): SearchError {
  const timestamp = Date.now();
  
  if (error instanceof Error) {
    // 网络错误
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return {
        type: SearchErrorType.NETWORK_ERROR,
        message: ERROR_MESSAGES[SearchErrorType.NETWORK_ERROR],
        originalError: error,
        retryable: true,
        timestamp,
      };
    }
    
    // 超时错误
    if (error.name === 'AbortError' || error.message.includes('timeout')) {
      return {
        type: SearchErrorType.TIMEOUT_ERROR,
        message: ERROR_MESSAGES[SearchErrorType.TIMEOUT_ERROR],
        originalError: error,
        retryable: true,
        timestamp,
      };
    }
    
    // 验证错误
    if (error.message.includes('validation') || error.message.includes('invalid')) {
      return {
        type: SearchErrorType.VALIDATION_ERROR,
        message: ERROR_MESSAGES[SearchErrorType.VALIDATION_ERROR],
        originalError: error,
        retryable: false,
        timestamp,
      };
    }
  }
  
  // 处理 HTTP 响应错误
  if (typeof error === 'object' && error !== null && 'status' in error) {
    const status = (error as any).status;
    
    if (status === 401 || status === 403) {
      return {
        type: SearchErrorType.AUTHORIZATION_ERROR,
        message: ERROR_MESSAGES[SearchErrorType.AUTHORIZATION_ERROR],
        code: status,
        retryable: false,
        timestamp,
      };
    }
    
    if (status >= 500) {
      return {
        type: SearchErrorType.SERVER_ERROR,
        message: ERROR_MESSAGES[SearchErrorType.SERVER_ERROR],
        code: status,
        retryable: true,
        timestamp,
      };
    }
    
    if (status >= 400) {
      return {
        type: SearchErrorType.VALIDATION_ERROR,
        message: ERROR_MESSAGES[SearchErrorType.VALIDATION_ERROR],
        code: status,
        retryable: false,
        timestamp,
      };
    }
  }
  
  // 默认为未知错误
  return {
    type: SearchErrorType.UNKNOWN_ERROR,
    message: ERROR_MESSAGES[SearchErrorType.UNKNOWN_ERROR],
    originalError: error instanceof Error ? error : new Error(String(error)),
    retryable: true,
    timestamp,
  };
}

// 重试策略配置
export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
  retryableErrors: SearchErrorType[];
}

export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2,
  retryableErrors: [
    SearchErrorType.NETWORK_ERROR,
    SearchErrorType.SERVER_ERROR,
    SearchErrorType.TIMEOUT_ERROR,
    SearchErrorType.UNKNOWN_ERROR,
  ],
};

// 重试机制
export class SearchRetryManager {
  private retryConfig: RetryConfig;
  private retryAttempts = new Map<string, number>();
  
  constructor(config: Partial<RetryConfig> = {}) {
    this.retryConfig = { ...DEFAULT_RETRY_CONFIG, ...config };
  }
  
  // 计算延迟时间
  private calculateDelay(attemptNumber: number): number {
    const delay = Math.min(
      this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffFactor, attemptNumber),
      this.retryConfig.maxDelay
    );
    
    // 添加随机抖动，避免雷群效应
    const jitter = delay * 0.1 * Math.random();
    return delay + jitter;
  }
  
  // 是否应该重试
  shouldRetry(error: SearchError, requestId: string): boolean {
    if (!error.retryable) {
      return false;
    }
    
    if (!this.retryConfig.retryableErrors.includes(error.type)) {
      return false;
    }
    
    const attempts = this.retryAttempts.get(requestId) || 0;
    return attempts < this.retryConfig.maxRetries;
  }
  
  // 执行重试
  async retry<T>(
    requestId: string,
    searchFunction: () => Promise<T>,
    onRetryAttempt?: (attempt: number, error: SearchError) => void
  ): Promise<T> {
    let lastError: SearchError;
    
    for (let attempt = 0; attempt <= this.retryConfig.maxRetries; attempt++) {
      try {
        this.retryAttempts.set(requestId, attempt);
        return await searchFunction();
      } catch (error) {
        lastError = classifyError(error);
        
        if (!this.shouldRetry(lastError, requestId)) {
          throw lastError;
        }
        
        if (onRetryAttempt) {
          onRetryAttempt(attempt + 1, lastError);
        }
        
        if (attempt < this.retryConfig.maxRetries) {
          const delay = this.calculateDelay(attempt);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    // 清理重试计数
    this.retryAttempts.delete(requestId);
    throw lastError!;
  }
  
  // 重置重试计数
  reset(requestId?: string): void {
    if (requestId) {
      this.retryAttempts.delete(requestId);
    } else {
      this.retryAttempts.clear();
    }
  }
}

// 全局重试管理器
export const searchRetryManager = new SearchRetryManager();

// 用户反馈工具
export class UserFeedbackManager {
  // 显示错误提示
  showError(error: SearchError, options: {
    showRetryButton?: boolean;
    onRetry?: () => void;
    duration?: number;
  } = {}): void {
    const { showRetryButton = false, onRetry, duration = 5000 } = options;
    
    // 创建重试按钮的配置信息
    const _actionConfig = showRetryButton && onRetry ? {
      text: '重试',
      onClick: onRetry,
      className: "inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
    } : undefined;
    
    toast({
      variant: 'destructive',
      title: '搜索失败',
      description: error.message,
      duration,
      // 注意：在 .ts 文件中不能直接使用 JSX，这里先移除 action
      // 如需要重试按钮功能，应该在 UI 组件层面实现
    });
    
    // 如果需要重试功能，可以在控制台记录或通过其他方式通知
    if (showRetryButton && onRetry) {
      console.log('重试选项可用，但在当前架构下需要在 UI 层面实现重试按钮');
    }
  }
  
  // 显示成功提示
  showSuccess(message: string, details?: string): void {
    toast({
      title: message,
      description: details,
      duration: 3000,
    });
  }
  
  // 显示警告提示
  showWarning(message: string, details?: string): void {
    toast({
      variant: 'default',
      title: message,
      description: details,
      duration: 4000,
    });
  }
  
  // 显示信息提示
  showInfo(message: string, details?: string): void {
    toast({
      title: message,
      description: details,
      duration: 3000,
    });
  }
  
  // 显示加载状态
  showLoading(message = '搜索中...'): { dismiss: () => void } {
    const _toastId = toast({
      title: message,
      description: '请稍候',
      duration: 0, // 不自动消失
    });
    
    return {
      dismiss: () => {
        // Toast 库通常会提供 dismiss 方法
        console.log('Loading dismissed');
      },
    };
  }
}

// 全局用户反馈管理器
export const userFeedback = new UserFeedbackManager();

// 搜索错误边界
export interface SearchErrorBoundaryState {
  hasError: boolean;
  error: SearchError | null;
  errorId: string | null;
}

export class SearchErrorRecovery {
  // 尝试从错误中恢复
  static async recoverFromError(
    error: SearchError,
    context: {
      searchParams?: Record<string, unknown>;
      retryFunction?: () => Promise<any>;
      fallbackFunction?: () => Promise<any>;
    }
  ): Promise<{ success: boolean; result?: any; fallbackUsed?: boolean }> {
    const { retryFunction, fallbackFunction } = context;
    
    // 如果错误可重试，先尝试重试
    if (error.retryable && retryFunction) {
      try {
        const result = await retryFunction();
        userFeedback.showSuccess('搜索已恢复');
        return { success: true, result };
      } catch (retryError) {
        console.warn('重试失败:', retryError);
      }
    }
    
    // 尝试使用备用方案
    if (fallbackFunction) {
      try {
        const result = await fallbackFunction();
        userFeedback.showWarning('使用备用搜索方案', '部分功能可能受限');
        return { success: true, result, fallbackUsed: true };
      } catch (fallbackError) {
        console.warn('备用方案失败:', fallbackError);
      }
    }
    
    // 无法恢复
    userFeedback.showError(error, {
      showRetryButton: error.retryable,
      onRetry: retryFunction,
    });
    
    return { success: false };
  }
}

// 搜索日志记录
export interface SearchLogEntry {
  id: string;
  timestamp: number;
  type: 'search' | 'error' | 'retry' | 'success';
  message: string;
  data?: Record<string, unknown>;
  error?: SearchError;
}

export class SearchLogger {
  private logs: SearchLogEntry[] = [];
  private maxLogs = 100;
  
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  }
  
  // 记录搜索日志
  log(type: SearchLogEntry['type'], message: string, data?: Record<string, unknown>, error?: SearchError): void {
    const entry: SearchLogEntry = {
      id: this.generateId(),
      timestamp: Date.now(),
      type,
      message,
      data,
      error,
    };
    
    this.logs.unshift(entry);
    
    // 保持日志数量限制
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs);
    }
    
    // 开发环境下输出到控制台
    if (process.env.NODE_ENV === 'development') {
      console.log(`[SearchLogger] ${type.toUpperCase()}: ${message}`, { data, error });
    }
  }
  
  // 获取日志
  getLogs(type?: SearchLogEntry['type']): SearchLogEntry[] {
    if (type) {
      return this.logs.filter(log => log.type === type);
    }
    return [...this.logs];
  }
  
  // 清空日志
  clear(): void {
    this.logs = [];
  }
  
  // 导出日志
  export(): string {
    return JSON.stringify(this.logs, null, 2);
  }
}

// 全局搜索日志器
export const searchLogger = new SearchLogger();

// 搜索健康检查
export class SearchHealthChecker {
  private healthStatus = {
    isHealthy: true,
    lastCheck: Date.now(),
    errorRate: 0,
    averageResponseTime: 0,
    consecutiveErrors: 0,
  };
  
  private errorCount = 0;
  private totalRequests = 0;
  private responseTimes: number[] = [];
  private maxResponseTimeHistory = 50;
  
  // 记录搜索结果
  recordSearch(success: boolean, responseTime: number): void {
    this.totalRequests++;
    this.responseTimes.push(responseTime);
    
    if (this.responseTimes.length > this.maxResponseTimeHistory) {
      this.responseTimes.shift();
    }
    
    if (success) {
      this.healthStatus.consecutiveErrors = 0;
    } else {
      this.errorCount++;
      this.healthStatus.consecutiveErrors++;
    }
    
    this.updateHealthStatus();
  }
  
  private updateHealthStatus(): void {
    this.healthStatus.lastCheck = Date.now();
    this.healthStatus.errorRate = this.totalRequests > 0 ? this.errorCount / this.totalRequests : 0;
    this.healthStatus.averageResponseTime = this.responseTimes.length > 0 
      ? this.responseTimes.reduce((sum, time) => sum + time, 0) / this.responseTimes.length 
      : 0;
    
    // 健康状态判断
    this.healthStatus.isHealthy = 
      this.healthStatus.errorRate < 0.5 && 
      this.healthStatus.consecutiveErrors < 5 &&
      this.healthStatus.averageResponseTime < 10000;
  }
  
  // 获取健康状态
  getHealthStatus(): typeof this.healthStatus {
    return { ...this.healthStatus };
  }
  
  // 重置统计
  reset(): void {
    this.errorCount = 0;
    this.totalRequests = 0;
    this.responseTimes = [];
    this.healthStatus = {
      isHealthy: true,
      lastCheck: Date.now(),
      errorRate: 0,
      averageResponseTime: 0,
      consecutiveErrors: 0,
    };
  }
}

// 全局健康检查器
export const searchHealthChecker = new SearchHealthChecker();

