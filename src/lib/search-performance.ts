/**
 * 搜索性能优化工具库
 * 提供防抖、缓存、预加载等性能优化功能
 */

import { useCallback, useRef, useMemo } from 'react';
import { debounce } from 'lodash';

// 缓存接口
interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiry: number;
}

// 搜索缓存管理器
export class SearchCache<T = unknown> {
  private cache = new Map<string, CacheItem<T>>();
  private defaultTTL: number;
  private maxSize: number;

  constructor(defaultTTL = 5 * 60 * 1000, maxSize = 100) {
    this.defaultTTL = defaultTTL;
    this.maxSize = maxSize;
  }

  // 生成缓存键
  private generateKey(params: Record<string, unknown>): string {
    return JSON.stringify(params, Object.keys(params).sort());
  }

  // 设置缓存
  set(params: Record<string, unknown>, data: T, ttl = this.defaultTTL): void {
    const key = this.generateKey(params);
    const now = Date.now();
    
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxSize) {
      const oldestKey = Array.from(this.cache.entries())
        .sort(([, a], [, b]) => a.timestamp - b.timestamp)[0]?.[0];
      
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }
    
    this.cache.set(key, {
      data,
      timestamp: now,
      expiry: now + ttl,
    });
  }

  // 获取缓存
  get(params: Record<string, unknown>): T | null {
    const key = this.generateKey(params);
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }
    
    // 检查是否过期
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }

  // 删除缓存
  delete(params: Record<string, unknown>): boolean {
    const key = this.generateKey(params);
    return this.cache.delete(key);
  }

  // 清空缓存
  clear(): void {
    this.cache.clear();
  }

  // 清理过期缓存
  cleanup(): number {
    const now = Date.now();
    let deletedCount = 0;
    
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key);
        deletedCount++;
      }
    }
    
    return deletedCount;
  }

  // 获取缓存统计信息
  getStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    oldestTimestamp: number;
    newestTimestamp: number;
  } {
    const items = Array.from(this.cache.values());
    const timestamps = items.map(item => item.timestamp);
    
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: 0, // 需要额外跟踪命中次数来计算
      oldestTimestamp: Math.min(...timestamps),
      newestTimestamp: Math.max(...timestamps),
    };
  }
}

// 全局搜索缓存实例
export const searchCache = new SearchCache();

// 防抖搜索 Hook
export interface UseDebounceSearchOptions {
  delay?: number;
  immediate?: boolean;
  maxWait?: number;
  leading?: boolean;
  trailing?: boolean;
}

export function useDebounceSearch<T extends (...args: any[]) => any>(
  searchFunction: T,
  options: UseDebounceSearchOptions = {}
) {
  const {
    delay = 300,
    immediate = false,
    maxWait,
    leading = false,
    trailing = true,
  } = options;

  const debouncedFn = useMemo(
    () => debounce(searchFunction, delay, {
      leading,
      trailing,
      maxWait,
    }),
    [searchFunction, delay, leading, trailing, maxWait]
  );

  // 立即执行函数
  const immediateSearch = useCallback((...args: Parameters<T>) => {
    debouncedFn.cancel();
    return searchFunction(...args);
  }, [searchFunction, debouncedFn]);

  return {
    debouncedSearch: debouncedFn,
    immediateSearch: immediate ? immediateSearch : debouncedFn,
    cancel: debouncedFn.cancel,
    flush: debouncedFn.flush,
  };
}

// 搜索请求去重
export class SearchDeduplicator {
  private pendingRequests = new Map<string, Promise<any>>();

  // 去重执行搜索
  async dedupedSearch<T>(
    key: string,
    searchFn: () => Promise<T>
  ): Promise<T> {
    // 如果有相同的请求正在进行，返回现有的 Promise
    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key) as Promise<T>;
    }

    // 创建新的搜索请求
    const searchPromise = searchFn()
      .finally(() => {
        // 请求完成后清理
        this.pendingRequests.delete(key);
      });

    this.pendingRequests.set(key, searchPromise);
    return searchPromise;
  }

  // 取消所有待处理的请求
  cancelAll(): void {
    this.pendingRequests.clear();
  }

  // 获取待处理请求数量
  getPendingCount(): number {
    return this.pendingRequests.size;
  }
}

// 全局搜索去重器
export const searchDeduplicator = new SearchDeduplicator();

// 搜索性能监控
export interface SearchMetrics {
  searchCount: number;
  totalDuration: number;
  averageDuration: number;
  cacheHitCount: number;
  cacheHitRate: number;
  errorCount: number;
  lastSearchTime: number;
}

export class SearchMetricsCollector {
  private metrics: SearchMetrics = {
    searchCount: 0,
    totalDuration: 0,
    averageDuration: 0,
    cacheHitCount: 0,
    cacheHitRate: 0,
    errorCount: 0,
    lastSearchTime: 0,
  };

  // 记录搜索开始
  startSearch(): { endSearch: (success: boolean, fromCache?: boolean) => void } {
    const startTime = performance.now();
    
    return {
      endSearch: (success: boolean, fromCache = false) => {
        const duration = performance.now() - startTime;
        
        this.metrics.searchCount++;
        this.metrics.lastSearchTime = Date.now();
        
        if (success) {
          if (fromCache) {
            this.metrics.cacheHitCount++;
          } else {
            this.metrics.totalDuration += duration;
          }
        } else {
          this.metrics.errorCount++;
        }
        
        // 重新计算平均时长和缓存命中率
        this.updateAverages();
      },
    };
  }

  private updateAverages(): void {
    const nonCachedSearches = this.metrics.searchCount - this.metrics.cacheHitCount;
    
    this.metrics.averageDuration = nonCachedSearches > 0 
      ? this.metrics.totalDuration / nonCachedSearches 
      : 0;
      
    this.metrics.cacheHitRate = this.metrics.searchCount > 0 
      ? this.metrics.cacheHitCount / this.metrics.searchCount 
      : 0;
  }

  // 获取性能指标
  getMetrics(): SearchMetrics {
    return { ...this.metrics };
  }

  // 重置指标
  reset(): void {
    this.metrics = {
      searchCount: 0,
      totalDuration: 0,
      averageDuration: 0,
      cacheHitCount: 0,
      cacheHitRate: 0,
      errorCount: 0,
      lastSearchTime: 0,
    };
  }
}

// 全局性能监控器
export const searchMetrics = new SearchMetricsCollector();

// 智能预加载
export class SearchPreloader {
  private preloadCache = new Map<string, Promise<any>>();
  private preloadHistory: string[] = [];
  private maxHistory = 10;

  // 预加载搜索结果
  async preload<T>(
    key: string,
    searchFn: () => Promise<T>,
    _priority = 0
  ): Promise<void> {
    // 如果已经在预加载，直接返回
    if (this.preloadCache.has(key)) {
      return;
    }

    // 创建预加载任务
    const preloadPromise = searchFn()
      .then(result => {
        // 预加载完成后，将结果放入主缓存
        const params = JSON.parse(key);
        searchCache.set(params, result);
        return result;
      })
      .catch(error => {
        console.warn('预加载失败:', error);
        throw error;
      })
      .finally(() => {
        // 清理预加载缓存
        this.preloadCache.delete(key);
      });

    this.preloadCache.set(key, preloadPromise);
    
    // 记录预加载历史
    this.preloadHistory.unshift(key);
    if (this.preloadHistory.length > this.maxHistory) {
      this.preloadHistory = this.preloadHistory.slice(0, this.maxHistory);
    }
  }

  // 基于搜索历史智能预加载
  smartPreload(
    currentParams: Record<string, unknown>,
    searchFn: (params: Record<string, unknown>) => Promise<any>
  ): void {
    // 根据当前搜索参数预测可能的下一步搜索
    const predictions = this.predictNextSearches(currentParams);
    
    predictions.forEach((params, index) => {
      // 降低优先级的预加载
      setTimeout(() => {
        const key = JSON.stringify(params, Object.keys(params).sort());
        this.preload(key, () => searchFn(params), -index);
      }, index * 100);
    });
  }

  private predictNextSearches(currentParams: Record<string, unknown>): Record<string, unknown>[] {
    const predictions: Record<string, unknown>[] = [];
    
    // 预测策略1：下一页
    if (currentParams.page && typeof currentParams.page === 'number') {
      predictions.push({
        ...currentParams,
        page: currentParams.page + 1,
      });
    }
    
    // 预测策略2：常用的排序方式
    const commonSorts = [
      { sortBy: 'created_at', sortOrder: 'desc' },
      { sortBy: 'updated_at', sortOrder: 'desc' },
      { sortBy: 'name', sortOrder: 'asc' },
    ];
    
    commonSorts.forEach(sort => {
      if (currentParams.sortBy !== sort.sortBy || currentParams.sortOrder !== sort.sortOrder) {
        predictions.push({
          ...currentParams,
          ...sort,
          page: 1, // 重置到第一页
        });
      }
    });
    
    return predictions.slice(0, 3); // 最多预加载3个
  }

  // 取消所有预加载
  cancelAll(): void {
    this.preloadCache.clear();
  }

  // 获取预加载状态
  getStatus(): {
    activePreloads: number;
    historyLength: number;
  } {
    return {
      activePreloads: this.preloadCache.size,
      historyLength: this.preloadHistory.length,
    };
  }
}

// 全局预加载器
export const searchPreloader = new SearchPreloader();

// 搜索性能优化 Hook
export interface UseSearchPerformanceOptions {
  enableCache?: boolean;
  enableDeduplication?: boolean;
  enableMetrics?: boolean;
  enablePreload?: boolean;
  debounceDelay?: number;
  cacheTimeout?: number;
}

export function useSearchPerformance<T extends Record<string, unknown>, R>(
  searchFunction: (params: T) => Promise<R>,
  options: UseSearchPerformanceOptions = {}
) {
  const {
    enableCache = true,
    enableDeduplication = true,
    enableMetrics = true,
    enablePreload = false,
    debounceDelay = 300,
    cacheTimeout = 5 * 60 * 1000,
  } = options;

  const abortControllerRef = useRef<AbortController | null>(null);

  const optimizedSearch = useCallback(async (params: T): Promise<R> => {
    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    abortControllerRef.current = new AbortController();
    const signal = abortControllerRef.current.signal;

    // 开始性能监控
    const metricsTracker = enableMetrics ? searchMetrics.startSearch() : null;

    try {
      // 检查缓存
      if (enableCache) {
        const cached = searchCache.get(params);
        if (cached) {
          metricsTracker?.endSearch(true, true);
          return cached as R;
        }
      }

      // 搜索函数包装
      const searchFn = async (): Promise<R> => {
        if (signal.aborted) {
          throw new Error('Search aborted');
        }
        
        const result = await searchFunction(params);
        
        if (signal.aborted) {
          throw new Error('Search aborted');
        }
        
        // 缓存结果
        if (enableCache) {
          searchCache.set(params, result, cacheTimeout);
        }
        
        return result;
      };

      // 去重搜索
      let result: R;
      if (enableDeduplication) {
        const key = JSON.stringify(params, Object.keys(params).sort());
        result = await searchDeduplicator.dedupedSearch(key, searchFn);
      } else {
        result = await searchFn();
      }

      // 智能预加载
      if (enablePreload) {
        searchPreloader.smartPreload(params as Record<string, unknown>, searchFunction as (params: Record<string, unknown>) => Promise<any>);
      }

      metricsTracker?.endSearch(true, false);
      return result;

    } catch (error) {
      metricsTracker?.endSearch(false, false);
      throw error;
    }
  }, [searchFunction, enableCache, enableDeduplication, enableMetrics, enablePreload, cacheTimeout]);

  // 防抖搜索
  const { debouncedSearch, immediateSearch, cancel } = useDebounceSearch(
    optimizedSearch,
    { delay: debounceDelay }
  );

  return {
    search: debouncedSearch,
    immediateSearch,
    cancel,
    clearCache: () => searchCache.clear(),
    getMetrics: () => searchMetrics.getMetrics(),
    resetMetrics: () => searchMetrics.reset(),
  };
}

// 搜索性能诊断工具
export function diagnoseSearchPerformance(): {
  cacheStats: ReturnType<SearchCache['getStats']>;
  metrics: SearchMetrics;
  preloadStatus: ReturnType<SearchPreloader['getStatus']>;
  pendingRequests: number;
  recommendations: string[];
} {
  const cacheStats = searchCache.getStats();
  const metrics = searchMetrics.getMetrics();
  const preloadStatus = searchPreloader.getStatus();
  const pendingRequests = searchDeduplicator.getPendingCount();
  
  const recommendations: string[] = [];
  
  // 生成优化建议
  if (metrics.cacheHitRate < 0.3) {
    recommendations.push('缓存命中率较低，考虑增加缓存时间或优化缓存策略');
  }
  
  if (metrics.averageDuration > 2000) {
    recommendations.push('搜索响应时间较慢，建议优化后端查询或增加索引');
  }
  
  if (metrics.errorCount > metrics.searchCount * 0.1) {
    recommendations.push('搜索错误率较高，建议检查网络连接和后端服务');
  }
  
  if (cacheStats.size === cacheStats.maxSize) {
    recommendations.push('缓存已满，考虑增加缓存大小或减少缓存时间');
  }
  
  return {
    cacheStats,
    metrics,
    preloadStatus,
    pendingRequests,
    recommendations,
  };
}



