/**
 * 搜索条件 URL 参数管理工具
 * 处理搜索条件的序列化/反序列化、URL 同步、压缩等功能
 */

import { 
  SearchCondition, 
  SearchFilters, 

  SearchState 
} from '@/types/search';

// URL 参数压缩映射
const URL_PARAM_MAPPING = {
  // 基础参数
  keyword: 'q',
  page: 'p',
  limit: 'l',
  sortBy: 's',
  sortOrder: 'o',
  
  // 复杂参数
  filters: 'f',
  conditions: 'c',
  
  // 特殊参数
  database: 'd',
  view: 'v',
} as const;

// 反向映射
const _REVERSE_URL_PARAM_MAPPING = Object.fromEntries(
  Object.entries(URL_PARAM_MAPPING).map(([key, value]) => [value, key])
);

// 操作符缩写映射
const OPERATOR_ABBREVIATIONS = {
  'contains': 'ct',
  'equals': 'eq',
  'startsWith': 'sw',
  'endsWith': 'ew',
  'notContains': 'nc',
  'before': 'bf',
  'after': 'af',
  'between': 'bt',
  'notEquals': 'ne',
  'greaterThan': 'gt',
  'lessThan': 'lt',
  'greaterThanOrEqual': 'gte',
  'lessThanOrEqual': 'lte',
} as const;

const REVERSE_OPERATOR_ABBREVIATIONS = Object.fromEntries(
  Object.entries(OPERATOR_ABBREVIATIONS).map(([key, value]) => [value, key])
);

// 逻辑操作符缩写
const LOGIC_ABBREVIATIONS = {
  'AND': 'A',
  'OR': 'O',
  'NOT': 'N',
} as const;

const REVERSE_LOGIC_ABBREVIATIONS = Object.fromEntries(
  Object.entries(LOGIC_ABBREVIATIONS).map(([key, value]) => [value, key])
);

/**
 * 压缩搜索条件以减少 URL 长度
 */
export function compressConditions(conditions: SearchCondition[]): string {
  try {
    const compressed = conditions.map(condition => ({
      i: condition.id,
      f: condition.field,
      o: OPERATOR_ABBREVIATIONS[condition.operator as keyof typeof OPERATOR_ABBREVIATIONS] || condition.operator,
      v: condition.value,
      l: condition.logic ? LOGIC_ABBREVIATIONS[condition.logic] : undefined,
    }));
    
    return JSON.stringify(compressed);
  } catch (error) {
    console.warn('Failed to compress conditions:', error);
    return JSON.stringify(conditions);
  }
}

/**
 * 解压搜索条件
 */
export function decompressConditions(compressed: string): SearchCondition[] {
  try {
    const parsed = JSON.parse(compressed);
    
    if (!Array.isArray(parsed)) {
      return [];
    }
    
    return parsed.map(item => {
      // 如果是新的压缩格式
      if (item.i && item.f && item.o) {
        return {
          id: item.i,
          field: item.f,
          operator: REVERSE_OPERATOR_ABBREVIATIONS[item.o] || item.o,
          value: item.v,
          logic: item.l ? REVERSE_LOGIC_ABBREVIATIONS[item.l] : undefined,
        };
      }
      
      // 兼容旧格式
      return item;
    });
  } catch (error) {
    console.warn('Failed to decompress conditions:', error);
    return [];
  }
}

/**
 * 压缩过滤器
 */
export function compressFilters(filters: SearchFilters): string {
  try {
    // 移除空值
    const cleanFilters: Record<string, unknown> = {};
    
    for (const [key, value] of Object.entries(filters)) {
      if (value !== undefined && value !== null && value !== '') {
        // 对数组进行特殊处理
        if (Array.isArray(value)) {
          if (value.length > 0) {
            cleanFilters[key] = value;
          }
        } else {
          cleanFilters[key] = value;
        }
      }
    }
    
    return JSON.stringify(cleanFilters);
  } catch (error) {
    console.warn('Failed to compress filters:', error);
    return JSON.stringify(filters);
  }
}

/**
 * 解压过滤器
 */
export function decompressFilters(compressed: string): SearchFilters {
  try {
    const parsed = JSON.parse(compressed);
    return typeof parsed === 'object' && parsed !== null ? parsed : {};
  } catch (error) {
    console.warn('Failed to decompress filters:', error);
    return {};
  }
}

/**
 * 搜索状态转换为 URL 参数
 */
export function searchStateToUrlParams(
  searchState: Partial<SearchState>,
  compress = true
): URLSearchParams {
  const params = new URLSearchParams();
  
  // 基础参数
  if (searchState.keyword) {
    params.set(URL_PARAM_MAPPING.keyword, searchState.keyword);
  }
  
  if (searchState.pagination?.page && searchState.pagination.page > 1) {
    params.set(URL_PARAM_MAPPING.page, searchState.pagination.page.toString());
  }
  
  if (searchState.pagination?.limit && searchState.pagination.limit !== 20) {
    params.set(URL_PARAM_MAPPING.limit, searchState.pagination.limit.toString());
  }
  
  if (searchState.sort?.field && searchState.sort.field !== '_score') {
    params.set(URL_PARAM_MAPPING.sortBy, searchState.sort.field);
  }
  
  if (searchState.sort?.order && searchState.sort.order !== 'desc') {
    params.set(URL_PARAM_MAPPING.sortOrder, searchState.sort.order);
  }
  
  if (searchState.currentDatabase) {
    params.set(URL_PARAM_MAPPING.database, searchState.currentDatabase);
  }
  
  // 复杂参数
  if (searchState.filters && Object.keys(searchState.filters).length > 0) {
    const filtersStr = compress 
      ? compressFilters(searchState.filters)
      : JSON.stringify(searchState.filters);
    params.set(URL_PARAM_MAPPING.filters, filtersStr);
  }
  
  if (searchState.conditions && searchState.conditions.length > 0) {
    const conditionsStr = compress
      ? compressConditions(searchState.conditions)
      : JSON.stringify(searchState.conditions);
    params.set(URL_PARAM_MAPPING.conditions, conditionsStr);
  }
  
  return params;
}

/**
 * URL 参数转换为搜索状态
 */
export function urlParamsToSearchState(params: URLSearchParams): Partial<SearchState> {
  const searchState: Partial<SearchState> = {};
  
  // 基础参数
  const keyword = params.get(URL_PARAM_MAPPING.keyword) || params.get('q');
  if (keyword) {
    searchState.keyword = keyword;
  }
  
  const page = params.get(URL_PARAM_MAPPING.page) || params.get('page') || params.get('p');
  const limit = params.get(URL_PARAM_MAPPING.limit) || params.get('limit') || params.get('l');
  
  if (page || limit) {
    searchState.pagination = {
      page: page ? Math.max(1, parseInt(page)) : 1,
      limit: limit ? Math.min(100, Math.max(1, parseInt(limit))) : 20,
    };
  }
  
  const sortBy = params.get(URL_PARAM_MAPPING.sortBy) || params.get('sortBy') || params.get('s');
  const sortOrder = params.get(URL_PARAM_MAPPING.sortOrder) || params.get('sortOrder') || params.get('o');
  
  if (sortBy || sortOrder) {
    searchState.sort = {
      field: sortBy || '_score',
      order: (sortOrder === 'asc' || sortOrder === 'desc') ? sortOrder : 'desc',
    };
  }
  
  const database = params.get(URL_PARAM_MAPPING.database) || params.get('database') || params.get('d');
  if (database) {
    searchState.currentDatabase = database;
  }
  
  // 复杂参数
  const filtersParam = params.get(URL_PARAM_MAPPING.filters) || params.get('filters') || params.get('f');
  if (filtersParam) {
    searchState.filters = decompressFilters(filtersParam);
  }
  
  const conditionsParam = params.get(URL_PARAM_MAPPING.conditions) || params.get('conditions') || params.get('c');
  if (conditionsParam) {
    searchState.conditions = decompressConditions(conditionsParam);
  }
  
  return searchState;
}

/**
 * 生成分享链接
 */
export function generateShareableUrl(
  baseUrl: string,
  searchState: Partial<SearchState>,
  options: {
    compress?: boolean;
    includeDatabase?: boolean;
    includePagination?: boolean;
  } = {}
): string {
  const {
    compress = true,
    includeDatabase = true,
    includePagination = false,
  } = options;
  
  const stateToShare: Partial<SearchState> = {
    keyword: searchState.keyword,
    conditions: searchState.conditions,
    filters: searchState.filters,
    sort: searchState.sort,
  };
  
  if (includeDatabase) {
    stateToShare.currentDatabase = searchState.currentDatabase;
  }
  
  if (includePagination) {
    stateToShare.pagination = searchState.pagination;
  }
  
  const params = searchStateToUrlParams(stateToShare, compress);
  const url = new URL(baseUrl);
  
  // 合并参数
  for (const [key, value] of params.entries()) {
    url.searchParams.set(key, value);
  }
  
  return url.toString();
}

/**
 * 验证 URL 参数的合法性
 */
export function validateUrlParams(params: URLSearchParams): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // 验证页码
  const page = params.get('p') || params.get('page');
  if (page) {
    const pageNum = parseInt(page);
    if (isNaN(pageNum) || pageNum < 1) {
      errors.push('Invalid page number');
    } else if (pageNum > 1000) {
      warnings.push('Page number is very large');
    }
  }
  
  // 验证分页大小
  const limit = params.get('l') || params.get('limit');
  if (limit) {
    const limitNum = parseInt(limit);
    if (isNaN(limitNum) || limitNum < 1) {
      errors.push('Invalid limit value');
    } else if (limitNum > 100) {
      warnings.push('Limit value is capped at 100');
    }
  }
  
  // 验证排序方向
  const sortOrder = params.get('o') || params.get('sortOrder');
  if (sortOrder && sortOrder !== 'asc' && sortOrder !== 'desc') {
    errors.push('Invalid sort order, must be "asc" or "desc"');
  }
  
  // 验证 JSON 参数
  const filtersParam = params.get('f') || params.get('filters');
  if (filtersParam) {
    try {
      JSON.parse(filtersParam);
    } catch {
      errors.push('Invalid filters JSON format');
    }
  }
  
  const conditionsParam = params.get('c') || params.get('conditions');
  if (conditionsParam) {
    try {
      const conditions = JSON.parse(conditionsParam);
      if (!Array.isArray(conditions)) {
        errors.push('Conditions must be an array');
      }
    } catch {
      errors.push('Invalid conditions JSON format');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * 计算 URL 复杂度分数 (用于决定是否需要压缩)
 */
export function calculateUrlComplexity(searchState: Partial<SearchState>): number {
  let score = 0;
  
  // 基础参数权重较低
  if (searchState.keyword) score += 1;
  if (searchState.pagination?.page && searchState.pagination.page > 1) score += 1;
  if (searchState.sort?.field !== '_score') score += 1;
  
  // 过滤器权重中等
  const filterCount = searchState.filters ? Object.keys(searchState.filters).length : 0;
  score += filterCount * 2;
  
  // 搜索条件权重较高
  const conditionCount = searchState.conditions ? searchState.conditions.length : 0;
  score += conditionCount * 3;
  
  return score;
}

/**
 * 优化 URL 参数 (移除默认值，压缩等)
 */
export function optimizeUrlParams(params: URLSearchParams): URLSearchParams {
  const optimized = new URLSearchParams();
  
  for (const [key, value] of params.entries()) {
    // 跳过默认值
    if (
      (key === 'p' || key === 'page') && value === '1' ||
      (key === 'l' || key === 'limit') && value === '20' ||
      (key === 'o' || key === 'sortOrder') && value === 'desc' ||
      (key === 's' || key === 'sortBy') && value === '_score'
    ) {
      continue;
    }
    
    // 保留其他参数
    optimized.set(key, value);
  }
  
  return optimized;
}

/**
 * 从当前页面 URL 中提取搜索状态
 */
export function extractSearchStateFromCurrentUrl(): Partial<SearchState> {
  if (typeof window === 'undefined') {
    return {};
  }
  
  const params = new URLSearchParams(window.location.search);
  return urlParamsToSearchState(params);
}

/**
 * 更新浏览器 URL 而不触发页面重新加载
 */
export function updateBrowserUrl(
  searchState: Partial<SearchState>,
  options: {
    replace?: boolean;
    compress?: boolean;
  } = {}
): void {
  if (typeof window === 'undefined' || !window.history) {
    return;
  }
  
  const { replace = false, compress = true } = options;
  const params = searchStateToUrlParams(searchState, compress);
  const optimizedParams = optimizeUrlParams(params);
  
  const url = new URL(window.location.href);
  url.search = optimizedParams.toString();
  
  if (replace) {
    window.history.replaceState(null, '', url.toString());
  } else {
    window.history.pushState(null, '', url.toString());
  }
}

/**
 * 检查两个搜索状态是否在 URL 同步方面相等
 */
export function areSearchStatesUrlEqual(
  state1: Partial<SearchState>,
  state2: Partial<SearchState>
): boolean {
  const params1 = searchStateToUrlParams(state1);
  const params2 = searchStateToUrlParams(state2);
  
  return params1.toString() === params2.toString();
}



