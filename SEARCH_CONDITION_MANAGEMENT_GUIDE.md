# 🔍 搜索条件管理系统使用指南

## 📖 概述

这个搜索条件管理系统为你的 Next.js + React + TypeScript 数据查询网站提供了完整的搜索条件管理解决方案，包括：

- **可视化搜索条件标签** - 以标签形式展示搜索条件，支持内联编辑
- **搜索历史记录** - 自动保存搜索历史，支持快速重用
- **搜索模板功能** - 保存常用搜索条件为模板
- **URL 同步** - 搜索条件与 URL 双向同步，支持分享链接
- **性能优化** - 防抖、缓存、去重等优化策略
- **错误处理** - 完善的错误处理和用户反馈机制

## 🚀 核心特性

### 1. 前端状态管理
- **Zustand + URL 双向同步**：搜索条件既存储在全局状态，又同步到 URL
- **类型安全**：完整的 TypeScript 类型定义
- **渐进式集成**：可与现有代码无缝集成

### 2. 用户体验优化
- **搜索条件可视化**：以标签形式展示，支持编辑和删除
- **智能防抖**：减少不必要的搜索请求
- **缓存机制**：重复搜索直接返回缓存结果
- **错误重试**：网络错误自动重试

### 3. 数据管理
- **搜索历史**：最近 20 次搜索记录
- **搜索模板**：最多 50 个保存的模板
- **数据持久化**：使用 localStorage 和 URL 参数

## 📁 文件结构

```
src/
├── types/
│   └── search.ts                    # 搜索相关类型定义
├── stores/
│   └── searchStore.ts               # Zustand 全局状态管理
├── hooks/
│   ├── use-search-manager.ts        # 主要的搜索管理 Hook
│   └── use-toast.tsx               # Toast 消息提示 Hook
├── components/
│   ├── SearchConditionTags.tsx     # 搜索条件标签组件
│   ├── SearchHistory.tsx           # 搜索历史组件
│   └── SearchTemplates.tsx         # 搜索模板组件
└── lib/
    ├── search-url-utils.ts          # URL 参数处理工具
    ├── search-performance.ts       # 性能优化工具
    └── search-error-handling.ts    # 错误处理工具
```

## 🛠️ 快速开始

### 1. 基础使用

```tsx
import { useSearchManager } from '@/hooks/use-search-manager';

function MySearchComponent() {
  const searchManager = useSearchManager({
    database: 'my_database',
    autoSyncUrl: true,
    loadFromUrl: true,
    onSearch: async (searchParams) => {
      // 你的搜索逻辑
      const response = await fetch('/api/search', {
        method: 'POST',
        body: JSON.stringify(searchParams),
      });
      
      const result = await response.json();
      return {
        success: result.success,
        data: result.data,
        total: result.total
      };
    },
  });

  return (
    <div>
      {/* 搜索条件标签展示 */}
      {searchManager.hasActiveSearch && (
        <SearchConditionTags
          keyword={searchManager.searchState.keyword}
          conditions={searchManager.searchState.conditions}
          filters={searchManager.searchState.filters}
          onKeywordChange={searchManager.setKeyword}
          onConditionRemove={searchManager.removeCondition}
          onFilterRemove={(key) => {
            const newFilters = { ...searchManager.searchState.filters };
            delete newFilters[key];
            searchManager.setFilters(newFilters);
          }}
          onClearAll={searchManager.resetSearch}
        />
      )}
      
      {/* 搜索按钮 */}
      <button onClick={searchManager.performSearch}>
        搜索
      </button>
    </div>
  );
}
```

### 2. 集成到现有页面

如果你已有搜索页面，可以这样集成：

```tsx
// 在现有的 DatabasePageContent.tsx 中
import { useSearchManager } from '@/hooks/use-search-manager';

export default function DatabasePageContent({ database }) {
  // 添加搜索管理器
  const searchManager = useSearchManager({
    database,
    autoSyncUrl: true,
    loadFromUrl: true,
    onSearch: async (searchParams) => {
      // 使用现有的搜索逻辑
      return await unifiedSearch({
        database: searchParams.database || database,
        advancedConditions: searchParams.conditions,
        filters: searchParams.filters,
        page: searchParams.page,
        limit: searchParams.limit,
        sortBy: searchParams.sortBy,
        sortOrder: searchParams.sortOrder,
      });
    },
  });

  // 现有代码保持不变...
  
  return (
    <div>
      {/* 在标题下方添加搜索条件标签 */}
      {searchManager.hasActiveSearch && (
        <div className="mt-3">
          <SearchConditionTags
            keyword={searchManager.searchState.keyword}
            conditions={searchManager.searchState.conditions}
            filters={searchManager.searchState.filters}
            onKeywordChange={searchManager.setKeyword}
            onKeywordRemove={() => searchManager.setKeyword('')}
            onConditionRemove={searchManager.removeCondition}
            onClearAll={searchManager.resetSearch}
            maxDisplayTags={8}
            compact
          />
        </div>
      )}
      
      {/* 现有的页面内容... */}
    </div>
  );
}
```

### 3. 搜索历史和模板

```tsx
import { SearchHistory, SearchTemplates } from '@/components';
import { useSearchHistory, useSearchTemplates } from '@/hooks/use-search-manager';

function SearchManagerPanel() {
  const searchHistory = useSearchHistory();
  const searchTemplates = useSearchTemplates();

  return (
    <div>
      {/* 搜索历史 */}
      <SearchHistory
        history={searchHistory.history}
        onLoadHistory={searchHistory.loadFromHistory}
        onRemoveHistory={searchHistory.removeHistoryItem}
        onClearHistory={searchHistory.clearHistory}
        maxItems={10}
        compact
      />
      
      {/* 搜索模板 */}
      <SearchTemplates
        templates={searchTemplates.templates}
        conditions={/* 当前搜索条件 */}
        filters={/* 当前过滤器 */}
        onLoadTemplate={searchTemplates.loadTemplate}
        onSaveTemplate={searchTemplates.saveAsTemplate}
        onDeleteTemplate={searchTemplates.deleteTemplate}
        maxItems={10}
        compact
      />
    </div>
  );
}
```

## 🎯 核心 API

### useSearchManager Hook

主要的搜索管理 Hook，提供完整的搜索状态管理功能。

```tsx
const searchManager = useSearchManager({
  database: string,              // 数据库标识
  autoSyncUrl?: boolean,         // 是否自动同步到 URL
  loadFromUrl?: boolean,         // 是否从 URL 加载初始状态
  onSearch?: Function,           // 搜索回调函数
  onSearchStateChange?: Function, // 搜索状态变更回调
  debounceDelay?: number,        // 防抖延迟时间
});

// 返回的方法和属性
searchManager = {
  // 状态
  searchState,           // 当前搜索状态
  hasActiveSearch,       // 是否有活跃搜索条件
  searchSummary,         // 搜索摘要文本
  activeFiltersCount,    // 活跃过滤器数量
  
  // 基础操作
  setKeyword,           // 设置关键词
  setFilters,           // 设置过滤器
  updateFilter,         // 更新单个过滤器
  clearFilters,         // 清空过滤器
  
  // 搜索条件操作
  addCondition,         // 添加搜索条件
  updateCondition,      // 更新搜索条件
  removeCondition,      // 删除搜索条件
  clearConditions,      // 清空搜索条件
  
  // 搜索执行
  performSearch,        // 执行搜索（带防抖）
  immediateSearch,      // 立即搜索（跳过防抖）
  
  // 历史和模板
  saveAsTemplate,       // 保存为模板
  loadTemplate,         // 加载模板
  loadFromHistory,      // 从历史记录加载
  
  // 实用功能
  resetSearch,          // 重置搜索
  syncToUrl,           // 同步到 URL
  getSearchMetrics,    // 获取搜索性能指标
};
```

### SearchConditionTags 组件

搜索条件标签显示组件。

```tsx
<SearchConditionTags
  keyword={string}                    // 关键词
  conditions={SearchCondition[]}      // 搜索条件数组
  filters={SearchFilters}            // 过滤器对象
  
  // 事件处理
  onKeywordChange?={(keyword) => void}
  onKeywordRemove?={() => void}
  onConditionEdit?={(condition) => void}
  onConditionRemove?={(conditionId) => void}
  onFilterEdit?={(key, value) => void}
  onFilterRemove?={(key) => void}
  onClearAll?={() => void}
  onAddCondition?={() => void}
  
  // 显示配置
  maxDisplayTags={number}             // 最大显示标签数
  showEditButtons={boolean}           // 是否显示编辑按钮
  showAddButton={boolean}             // 是否显示添加按钮
  compact={boolean}                   // 紧凑模式
/>
```

## ⚡ 性能优化

### 1. 防抖处理
系统自动对搜索请求进行防抖处理，默认延迟 300ms。

### 2. 缓存机制
- 搜索结果缓存 5 分钟
- 自动清理过期缓存
- 支持手动清理缓存

### 3. 请求去重
相同的搜索请求会被自动去重，避免重复请求。

### 4. 智能预加载
基于用户行为预测下一步搜索，提前加载结果。

```tsx
// 获取性能指标
const metrics = searchManager.getSearchMetrics();
console.log('缓存命中率:', metrics.cacheHitRate);
console.log('平均响应时间:', metrics.averageDuration);

// 性能诊断
const diagnosis = searchManager.getPerformanceDiagnosis();
console.log('优化建议:', diagnosis.recommendations);
```

## 🛡️ 错误处理

### 1. 自动重试
网络错误和服务器错误会自动重试，最多 3 次。

### 2. 用户友好的错误提示
- 网络错误：显示网络连接提示
- 权限错误：提示登录
- 服务器错误：建议稍后重试

### 3. 错误日志
所有搜索错误都会被记录，便于调试。

```tsx
// 获取错误日志
const logs = searchManager.getSearchLogs();
console.log('错误日志:', logs.filter(log => log.type === 'error'));

// 获取健康状态
const health = searchManager.getHealthStatus();
console.log('搜索健康状态:', health.isHealthy);
```

## 🎨 自定义样式

所有组件都使用 Tailwind CSS，你可以通过以下方式自定义样式：

### 1. 传递自定义 className

```tsx
<SearchConditionTags
  className="my-custom-search-tags"
  // 其他 props...
/>
```

### 2. 覆盖默认样式

```css
/* 在你的 CSS 文件中 */
.my-custom-search-tags .tag-item {
  @apply bg-blue-100 text-blue-800;
}
```

## 🔧 配置选项

### 搜索配置

```tsx
// 在 src/types/search.ts 中修改默认配置
export const DEFAULT_SEARCH_CONFIG = {
  maxHistoryItems: 20,              // 最大历史记录数
  historyTTL: 7 * 24 * 60 * 60 * 1000, // 历史记录保存时长
  maxTemplates: 50,                 // 最大模板数
  enableCache: true,                // 是否启用缓存
  debounceDelay: 300,              // 防抖延迟
  // ...其他配置
};
```

### 性能配置

```tsx
const searchManager = useSearchManager({
  // ...其他配置
  debounceDelay: 500,              // 自定义防抖延迟
});
```

## 📱 移动端支持

系统已完全适配移动端：

- **响应式设计**：所有组件都支持移动端
- **触摸优化**：按钮和交互元素适合手指操作
- **Sheet 弹窗**：移动端使用 Sheet 组件展示搜索管理面板

## 🚀 部署注意事项

### 1. 环境变量
确保设置了正确的环境变量：

```env
NEXT_PUBLIC_API_URL=your_api_url
```

### 2. 服务端渲染
系统支持 SSR，URL 参数会在服务端正确解析。

### 3. 缓存策略
生产环境建议：
- 减少缓存时间到 2-3 分钟
- 定期清理过期缓存
- 监控缓存命中率

## 🔍 故障排查

### 常见问题

1. **搜索条件不同步到 URL**
   - 检查 `autoSyncUrl` 是否设置为 `true`
   - 确保组件在 Next.js 路由内

2. **缓存不生效**
   - 检查搜索参数是否完全相同
   - 验证缓存时间设置

3. **错误重试不工作**
   - 查看错误类型是否在可重试列表中
   - 检查网络连接

### 调试工具

```tsx
// 开启调试模式
if (process.env.NODE_ENV === 'development') {
  // 查看搜索状态
  console.log('搜索状态:', searchManager.searchState);
  
  // 查看性能指标
  console.log('性能指标:', searchManager.getSearchMetrics());
  
  // 查看错误日志
  console.log('错误日志:', searchManager.getSearchLogs());
}
```

## 🎯 最佳实践

### 1. 搜索条件设计
- 保持搜索条件简洁明了
- 为常用搜索创建模板
- 合理设置默认值

### 2. 性能优化
- 避免过于频繁的搜索请求
- 合理使用缓存
- 监控搜索性能指标

### 3. 用户体验
- 提供清晰的搜索状态反馈
- 支持快速清空和重置
- 保存用户的搜索偏好

### 4. 错误处理
- 提供友好的错误信息
- 支持错误重试
- 记录错误日志用于调试

## 📝 总结

这个搜索条件管理系统提供了完整的前端搜索解决方案，包括状态管理、URL 同步、性能优化和错误处理。系统设计灵活，可以渐进式集成到现有项目中，显著提升用户的搜索体验。

通过合理配置和使用，你可以为用户提供：
- **直观的搜索条件管理**
- **便捷的搜索历史和模板功能**  
- **流畅的搜索体验**
- **可靠的错误处理**

开始使用吧！🚀



